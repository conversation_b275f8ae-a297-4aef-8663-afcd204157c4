{"name": "@kodekilat/studio", "version": "1.0.0", "description": "KodeKilat Studio ⚡ - Main Electron Application", "main": "main.js", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start", "electron": "electron .", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "dist:linux": "npm run build && electron-builder --linux", "pack": "npm run build && electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "electron": "^27.0.0", "electron-is-dev": "^2.0.0", "electron-next": "^3.1.5", "@monaco-editor/react": "^4.6.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0", "framer-motion": "^10.16.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "lucide-react": "^0.292.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "react-hotkeys-hook": "^4.4.1", "react-split": "^2.0.14", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "zustand": "^4.4.6", "immer": "^10.0.3", "nanoid": "^5.0.3", "fuse.js": "^7.0.0", "chokidar": "^3.5.3", "mime-types": "^2.1.35", "file-icons-js": "^1.0.3"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/node": "^20.10.0", "@types/mime-types": "^2.1.4", "typescript": "^5.3.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "electron-builder": "^24.6.4", "concurrently": "^8.2.2", "wait-on": "^7.2.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.0"}, "build": {"appId": "com.kodekilat.studio", "productName": "KodeKilat Studio", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", ".next/**/*", "node_modules/**/*", "!node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!node_modules/*.d.ts", "!node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/icon.ico"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.developer-tools"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Development"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "keywords": ["ide", "editor", "electron", "nextjs", "ai", "cyberpunk", "nusantara"]}