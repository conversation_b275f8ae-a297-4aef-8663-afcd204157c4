/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-editor .inlineEditHints.withBorder {
	z-index: 39;
	color: var(--vscode-editorHoverWidget-foreground);
	background-color: var(--vscode-editorHoverWidget-background);
	border: 1px solid var(--vscode-editorHoverWidget-border);
}

.monaco-editor .inlineEditHints a {
	color: var(--vscode-foreground);
}

.monaco-editor .inlineEditHints a:hover {
	color: var(--vscode-foreground);
}

.monaco-editor .inlineEditHints .keybinding {
	display: flex;
	margin-left: 4px;
	opacity: 0.6;
}

.monaco-editor .inlineEditHints .keybinding .monaco-keybinding-key {
	font-size: 8px;
	padding: 2px 3px;
}

.monaco-editor .inlineEditStatusBarItemLabel {
	margin-right: 2px;
}
