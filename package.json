{"name": "kodekilat-studio", "version": "1.0.0", "description": "KodeKilat Studio ⚡ - IDE Desktop Lokal Nusantara dengan AI dan UI Futuristik", "main": "apps/studio/main.js", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "bun run --cwd apps/studio dev", "build": "bun run build:packages && bun run build:apps", "build:packages": "bun run --filter './packages/*' build", "build:apps": "bun run --filter './apps/*' build", "start": "bun run --cwd apps/studio start", "electron": "bun run --cwd apps/studio electron", "electron:dev": "bun run --cwd apps/studio electron:dev", "dist": "bun run --cwd apps/studio dist", "test": "bun test", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "format": "prettier --write .", "clean": "rimraf node_modules apps/*/node_modules packages/*/node_modules apps/*/dist packages/*/dist"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.0", "eslint-config-prettier": "^9.0.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "keywords": ["ide", "editor", "electron", "nextjs", "ai", "cyberpunk", "nusantara", "vscode", "monaco", "terminal"], "author": "KodeKilat Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/kodekilat/studio.git"}}