'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Icon } from '@/components/ui/icon';
import { useAppStore } from '@/stores/app-store';
import { LoginForm } from '@/components/auth/login-form';
import { RegisterForm } from '@/components/auth/register-form';
import {
  FolderOpen,
  FileText,
  GitBranch,
  Zap,
  Settings,
  User
} from 'lucide-react';

type AuthMode = 'none' | 'login' | 'register';

export function WelcomeScreen() {
  const [authMode, setAuthMode] = useState<AuthMode>('none');
  const { openWorkspace, recentWorkspaces } = useAppStore();

  const handleOpenFolder = async () => {
    // Mock folder selection - in real app would use Electron dialog
    const mockPath = '/Users/<USER>/projects/my-app';
    await openWorkspace(mockPath);
  };

  // Show auth modal
  if (authMode !== 'none') {
    return (
      <div className="fixed inset-0 bg-cyber-bg cyber-grid flex items-center justify-center">
        <AnimatePresence mode="wait">
          {authMode === 'login' && (
            <LoginForm
              key="login"
              onSwitchToRegister={() => setAuthMode('register')}
              onClose={() => setAuthMode('none')}
            />
          )}
          {authMode === 'register' && (
            <RegisterForm
              key="register"
              onSwitchToLogin={() => setAuthMode('login')}
              onClose={() => setAuthMode('none')}
            />
          )}
        </AnimatePresence>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-cyber-bg cyber-grid">
      {/* Header */}
      <div className="h-16 border-b border-cyber-border bg-cyber-surface/50 backdrop-blur-sm flex items-center justify-between px-6">
        <div className="flex items-center space-x-3">
          <div className="text-2xl">⚡</div>
          <div>
            <h1 className="text-lg font-cyber font-bold text-neon-cyan">KodeKilat Studio</h1>
            <p className="text-xs text-cyber-muted">IDE Desktop Lokal Nusantara</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={() => setAuthMode('login')}>
            <Icon icon={User} size={16} className="mr-2" />
            Sign In
          </Button>
          <Button variant="ghost" size="sm">
            <Icon icon={Settings} size={16} />
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Left Panel - Quick Actions */}
        <div className="w-80 border-r border-cyber-border bg-cyber-surface/30 p-6">
          <h2 className="text-lg font-semibold text-cyber-text mb-6">Get Started</h2>
          
          <div className="space-y-4">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                variant="cyber"
                className="w-full justify-start h-12"
                onClick={handleOpenFolder}
                glow
              >
                <Icon icon={FolderOpen} size={20} className="mr-3" />
                <div className="text-left">
                  <div className="font-medium">Open Folder</div>
                  <div className="text-xs text-cyber-muted">Start coding in existing project</div>
                </div>
              </Button>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button variant="outline" className="w-full justify-start h-12">
                <Icon icon={FileText} size={20} className="mr-3" />
                <div className="text-left">
                  <div className="font-medium">New File</div>
                  <div className="text-xs text-cyber-muted">Create a new file</div>
                </div>
              </Button>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button variant="outline" className="w-full justify-start h-12">
                <Icon icon={GitBranch} size={20} className="mr-3" />
                <div className="text-left">
                  <div className="font-medium">Clone Repository</div>
                  <div className="text-xs text-cyber-muted">Clone from Git repository</div>
                </div>
              </Button>
            </motion.div>
          </div>

          {/* Recent Workspaces */}
          {recentWorkspaces.length > 0 && (
            <div className="mt-8">
              <h3 className="text-sm font-medium text-cyber-text mb-4">Recent Workspaces</h3>
              <div className="space-y-2">
                {recentWorkspaces.slice(0, 5).map((workspace) => (
                  <motion.button
                    key={workspace.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => openWorkspace(workspace.path)}
                    className="w-full text-left p-3 rounded border border-cyber-border hover:border-neon-cyan hover:bg-cyber-surface/50 transition-all duration-200"
                  >
                    <div className="font-medium text-cyber-text truncate">{workspace.name}</div>
                    <div className="text-xs text-cyber-muted truncate">{workspace.path}</div>
                    <div className="text-xs text-cyber-muted mt-1">
                      {workspace.lastOpened.toLocaleDateString()}
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Right Panel - Features & Info */}
        <div className="flex-1 p-8">
          <div className="max-w-2xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-center mb-12"
            >
              <div className="text-6xl mb-6 animate-neon-flicker">⚡</div>
              <h1 className="text-4xl font-cyber font-bold text-neon-cyan mb-4">
                KodeKilat Studio
              </h1>
              <p className="text-xl text-cyber-text mb-2">
                IDE Desktop Lokal Nusantara
              </p>
              <p className="text-cyber-muted">
                Dengan AI, UI Futuristik, dan Kemampuan Offline-First
              </p>
            </motion.div>

            {/* Features Grid */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="grid grid-cols-2 gap-6 mb-8"
            >
              <div className="p-6 border border-cyber-border rounded-lg bg-cyber-surface/30 hover:bg-cyber-surface/50 transition-all duration-300">
                <Icon icon={Zap} size={24} className="text-neon-cyan mb-3" glow />
                <h3 className="font-semibold text-cyber-text mb-2">AI-Powered</h3>
                <p className="text-sm text-cyber-muted">
                  Multi-provider AI integration dengan OpenAI, Claude, HuggingFace, dan OpenRouter
                </p>
              </div>

              <div className="p-6 border border-cyber-border rounded-lg bg-cyber-surface/30 hover:bg-cyber-surface/50 transition-all duration-300">
                <Icon icon={FolderOpen} size={24} className="text-neon-purple mb-3" glow />
                <h3 className="font-semibold text-cyber-text mb-2">VSCode Compatible</h3>
                <p className="text-sm text-cyber-muted">
                  Mendukung .vsix extensions dan shortcut familiar dari VSCode
                </p>
              </div>

              <div className="p-6 border border-cyber-border rounded-lg bg-cyber-surface/30 hover:bg-cyber-surface/50 transition-all duration-300">
                <Icon icon={GitBranch} size={24} className="text-neon-green mb-3" glow />
                <h3 className="font-semibold text-cyber-text mb-2">Git Integration</h3>
                <p className="text-sm text-cyber-muted">
                  Visual Git interface dengan CLI backend untuk version control
                </p>
              </div>

              <div className="p-6 border border-cyber-border rounded-lg bg-cyber-surface/30 hover:bg-cyber-surface/50 transition-all duration-300">
                <Icon icon={FileText} size={24} className="text-neon-orange mb-3" glow />
                <h3 className="font-semibold text-cyber-text mb-2">Monaco Editor</h3>
                <p className="text-sm text-cyber-muted">
                  Editor berkualitas tinggi dengan syntax highlighting dan IntelliSense
                </p>
              </div>
            </motion.div>

            {/* Version Info */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6 }}
              className="text-center text-sm text-cyber-muted"
            >
              <p>Version 1.0.0 • Built with Electron + Next.js + Bun</p>
              <p className="mt-1">Made with ❤️ in Indonesia 🇮🇩</p>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
