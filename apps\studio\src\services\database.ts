// KodeKilat Studio ⚡ - Database Service
// Client-side database service yang berkomunikasi dengan Electron main process

export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  createdAt: Date;
  lastLogin: Date;
}

export interface Session {
  id: string;
  userId: string;
  token: string;
  expiresAt: Date;
  isActive: boolean;
}

export interface Workspace {
  id: string;
  name: string;
  path: string;
  lastOpened: Date;
  isActive: boolean;
  settings: any;
}

class DatabaseService {
  private static instance: DatabaseService;

  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  // User Management
  async createUser(userData: {
    username: string;
    email: string;
    password: string;
    avatar?: string;
  }): Promise<User> {
    try {
      // Hash password
      const passwordHash = await this.hashPassword(userData.password);
      
      const user = {
        id: this.generateId(),
        username: userData.username,
        email: userData.email,
        passwordHash,
        avatar: userData.avatar,
        createdAt: new Date(),
        lastLogin: new Date()
      };

      // Store in localStorage for now (will be replaced with SQLite via IPC)
      const users = this.getStoredUsers();
      users.push(user);
      localStorage.setItem('kodekilat-users', JSON.stringify(users));

      return {
        id: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        createdAt: user.createdAt,
        lastLogin: user.lastLogin
      };
    } catch (error) {
      throw new Error(`Failed to create user: ${error}`);
    }
  }

  async authenticateUser(email: string, password: string): Promise<User | null> {
    try {
      const users = this.getStoredUsers();
      const user = users.find(u => u.email === email);
      
      if (!user) {
        return null;
      }

      const isValidPassword = await this.verifyPassword(password, user.passwordHash);
      if (!isValidPassword) {
        return null;
      }

      // Update last login
      user.lastLogin = new Date();
      const userIndex = users.findIndex(u => u.id === user.id);
      users[userIndex] = user;
      localStorage.setItem('kodekilat-users', JSON.stringify(users));

      return {
        id: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        createdAt: new Date(user.createdAt),
        lastLogin: new Date(user.lastLogin)
      };
    } catch (error) {
      throw new Error(`Authentication failed: ${error}`);
    }
  }

  async getUserByEmail(email: string): Promise<User | null> {
    const users = this.getStoredUsers();
    const user = users.find(u => u.email === email);
    
    if (!user) return null;

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      avatar: user.avatar,
      createdAt: new Date(user.createdAt),
      lastLogin: new Date(user.lastLogin)
    };
  }

  // Session Management
  async createSession(userId: string): Promise<Session> {
    const session = {
      id: this.generateId(),
      userId,
      token: this.generateToken(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      isActive: true
    };

    const sessions = this.getStoredSessions();
    sessions.push(session);
    localStorage.setItem('kodekilat-sessions', JSON.stringify(sessions));

    return session;
  }

  async getSessionByToken(token: string): Promise<Session | null> {
    const sessions = this.getStoredSessions();
    const session = sessions.find(s => s.token === token && s.isActive);
    
    if (!session) return null;
    
    // Check if session is expired
    if (new Date(session.expiresAt) < new Date()) {
      await this.invalidateSession(token);
      return null;
    }

    return {
      ...session,
      expiresAt: new Date(session.expiresAt)
    };
  }

  async invalidateSession(token: string): Promise<void> {
    const sessions = this.getStoredSessions();
    const sessionIndex = sessions.findIndex(s => s.token === token);
    
    if (sessionIndex >= 0) {
      sessions[sessionIndex].isActive = false;
      localStorage.setItem('kodekilat-sessions', JSON.stringify(sessions));
    }
  }

  // Workspace Management
  async createWorkspace(workspaceData: {
    name: string;
    path: string;
    settings?: any;
  }): Promise<Workspace> {
    const workspace = {
      id: this.generateId(),
      name: workspaceData.name,
      path: workspaceData.path,
      lastOpened: new Date(),
      isActive: false,
      settings: workspaceData.settings || {}
    };

    const workspaces = this.getStoredWorkspaces();
    workspaces.push(workspace);
    localStorage.setItem('kodekilat-workspaces', JSON.stringify(workspaces));

    return workspace;
  }

  async getWorkspaces(): Promise<Workspace[]> {
    const workspaces = this.getStoredWorkspaces();
    return workspaces.map(w => ({
      ...w,
      lastOpened: new Date(w.lastOpened)
    }));
  }

  async setActiveWorkspace(workspaceId: string): Promise<void> {
    const workspaces = this.getStoredWorkspaces();
    
    // Deactivate all workspaces
    workspaces.forEach(w => w.isActive = false);
    
    // Activate selected workspace
    const workspace = workspaces.find(w => w.id === workspaceId);
    if (workspace) {
      workspace.isActive = true;
      workspace.lastOpened = new Date();
    }

    localStorage.setItem('kodekilat-workspaces', JSON.stringify(workspaces));
  }

  // Helper methods
  private getStoredUsers(): any[] {
    const stored = localStorage.getItem('kodekilat-users');
    return stored ? JSON.parse(stored) : [];
  }

  private getStoredSessions(): any[] {
    const stored = localStorage.getItem('kodekilat-sessions');
    return stored ? JSON.parse(stored) : [];
  }

  private getStoredWorkspaces(): any[] {
    const stored = localStorage.getItem('kodekilat-workspaces');
    return stored ? JSON.parse(stored) : [];
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private generateToken(): string {
    return Array.from(crypto.getRandomValues(new Uint8Array(32)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  private async hashPassword(password: string): Promise<string> {
    // Simple hash for demo - in production use proper bcrypt
    const encoder = new TextEncoder();
    const data = encoder.encode(password + 'kodekilat-salt');
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  private async verifyPassword(password: string, hash: string): Promise<boolean> {
    const passwordHash = await this.hashPassword(password);
    return passwordHash === hash;
  }

  // Initialize demo data
  async initializeDemoData(): Promise<void> {
    const users = this.getStoredUsers();
    if (users.length === 0) {
      // Create demo user
      await this.createUser({
        username: 'developer',
        email: '<EMAIL>',
        password: 'password123',
        avatar: '⚡'
      });
    }
  }
}

export const databaseService = DatabaseService.getInstance();
