// KodeKilat Studio ⚡ - Authentication Service
import { databaseService, type User, type Session } from './database';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  avatar?: string;
}

export interface AuthState {
  user: User | null;
  session: Session | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

class AuthService {
  private static instance: AuthService;
  private currentSession: Session | null = null;

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  async login(credentials: LoginCredentials): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      // Validate input
      if (!credentials.email || !credentials.password) {
        return { success: false, error: 'Email and password are required' };
      }

      if (!this.isValidEmail(credentials.email)) {
        return { success: false, error: 'Invalid email format' };
      }

      // Authenticate user
      const user = await databaseService.authenticateUser(credentials.email, credentials.password);
      
      if (!user) {
        return { success: false, error: 'Invalid email or password' };
      }

      // Create session
      const session = await databaseService.createSession(user.id);
      this.currentSession = session;

      // Store session token
      localStorage.setItem('kodekilat-session-token', session.token);

      return { success: true, user };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Login failed. Please try again.' };
    }
  }

  async register(data: RegisterData): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      // Validate input
      const validation = this.validateRegistrationData(data);
      if (!validation.isValid) {
        return { success: false, error: validation.error };
      }

      // Check if user already exists
      const existingUser = await databaseService.getUserByEmail(data.email);
      if (existingUser) {
        return { success: false, error: 'User with this email already exists' };
      }

      // Create user
      const user = await databaseService.createUser({
        username: data.username,
        email: data.email,
        password: data.password,
        avatar: data.avatar
      });

      // Auto-login after registration
      const session = await databaseService.createSession(user.id);
      this.currentSession = session;

      // Store session token
      localStorage.setItem('kodekilat-session-token', session.token);

      return { success: true, user };
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Registration failed. Please try again.' };
    }
  }

  async logout(): Promise<void> {
    try {
      // Invalidate current session
      if (this.currentSession) {
        await databaseService.invalidateSession(this.currentSession.token);
        this.currentSession = null;
      }

      // Clear stored session token
      localStorage.removeItem('kodekilat-session-token');
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  async getCurrentUser(): Promise<User | null> {
    try {
      const token = localStorage.getItem('kodekilat-session-token');
      if (!token) {
        return null;
      }

      const session = await databaseService.getSessionByToken(token);
      if (!session) {
        // Session expired or invalid
        localStorage.removeItem('kodekilat-session-token');
        return null;
      }

      this.currentSession = session;

      // Get user data
      const users = JSON.parse(localStorage.getItem('kodekilat-users') || '[]');
      const userData = users.find((u: any) => u.id === session.userId);
      
      if (!userData) {
        return null;
      }

      return {
        id: userData.id,
        username: userData.username,
        email: userData.email,
        avatar: userData.avatar,
        createdAt: new Date(userData.createdAt),
        lastLogin: new Date(userData.lastLogin)
      };
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  async refreshSession(): Promise<boolean> {
    try {
      const user = await this.getCurrentUser();
      return !!user;
    } catch (error) {
      console.error('Refresh session error:', error);
      return false;
    }
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.currentSession) {
        return { success: false, error: 'Not authenticated' };
      }

      // Validate new password
      if (!this.isValidPassword(newPassword)) {
        return { success: false, error: 'Password must be at least 8 characters long' };
      }

      // Get current user
      const user = await this.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not found' };
      }

      // Verify current password
      const authResult = await databaseService.authenticateUser(user.email, currentPassword);
      if (!authResult) {
        return { success: false, error: 'Current password is incorrect' };
      }

      // Update password (simplified - in real app would update in database)
      const users = JSON.parse(localStorage.getItem('kodekilat-users') || '[]');
      const userIndex = users.findIndex((u: any) => u.id === user.id);
      
      if (userIndex >= 0) {
        const encoder = new TextEncoder();
        const data = encoder.encode(newPassword + 'kodekilat-salt');
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const passwordHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        
        users[userIndex].passwordHash = passwordHash;
        localStorage.setItem('kodekilat-users', JSON.stringify(users));
      }

      return { success: true };
    } catch (error) {
      console.error('Change password error:', error);
      return { success: false, error: 'Failed to change password' };
    }
  }

  // Validation helpers
  private validateRegistrationData(data: RegisterData): { isValid: boolean; error?: string } {
    if (!data.username || data.username.length < 3) {
      return { isValid: false, error: 'Username must be at least 3 characters long' };
    }

    if (!data.email || !this.isValidEmail(data.email)) {
      return { isValid: false, error: 'Invalid email format' };
    }

    if (!data.password || !this.isValidPassword(data.password)) {
      return { isValid: false, error: 'Password must be at least 8 characters long' };
    }

    if (data.password !== data.confirmPassword) {
      return { isValid: false, error: 'Passwords do not match' };
    }

    return { isValid: true };
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidPassword(password: string): boolean {
    return password.length >= 8;
  }

  // Initialize demo data
  async initializeDemo(): Promise<void> {
    await databaseService.initializeDemoData();
  }

  // Get session info
  getCurrentSession(): Session | null {
    return this.currentSession;
  }

  isSessionValid(): boolean {
    if (!this.currentSession) return false;
    return new Date(this.currentSession.expiresAt) > new Date();
  }
}

export const authService = AuthService.getInstance();
