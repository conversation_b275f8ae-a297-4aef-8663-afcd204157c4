'use client';

import { Inter } from 'next/font/google';
import '../styles/globals.css';
import { ThemeProvider } from '../components/providers/theme-provider';
import { StoreProvider } from '../components/providers/store-provider';

const inter = Inter({ subsets: ['latin'] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="dark">
      <head>
        <title>KodeKilat Studio ⚡</title>
        <meta name="description" content="IDE Desktop Lokal Nusantara dengan AI dan UI Futuristik" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className={`${inter.className} theme-cyberpunk`}>
        <StoreProvider>
          <ThemeProvider>
            {children}
          </ThemeProvider>
        </StoreProvider>
      </body>
    </html>
  );
}
