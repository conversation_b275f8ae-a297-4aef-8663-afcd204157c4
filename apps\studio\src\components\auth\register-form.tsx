'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Icon } from '@/components/ui/icon';
import { useAppStore } from '@/stores/app-store';
import {
  User,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Loader2,
  AlertCircle,
  CheckCircle,
  Sparkles
} from 'lucide-react';

interface RegisterFormProps {
  onSwitchToLogin: () => void;
  onClose?: () => void;
}

export function RegisterForm({ onSwitchToLogin, onClose }: RegisterFormProps) {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    avatar: '⚡'
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const { register } = useAppStore();

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError(''); // Clear error when user types
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      const result = await register(formData);
      
      if (result.success) {
        onClose?.();
      } else {
        setError(result.error || 'Registration failed');
      }
    } catch (error) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const avatarOptions = ['⚡', '🚀', '💻', '🔥', '⭐', '🎯', '🌟', '💎', '🎨', '🔮'];

  const getPasswordStrength = (password: string) => {
    if (password.length === 0) return { strength: 0, label: '', color: '' };
    if (password.length < 6) return { strength: 1, label: 'Weak', color: 'text-red-400' };
    if (password.length < 10) return { strength: 2, label: 'Medium', color: 'text-yellow-400' };
    return { strength: 3, label: 'Strong', color: 'text-green-400' };
  };

  const passwordStrength = getPasswordStrength(formData.password);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="w-full max-w-md p-8 bg-cyber-surface border border-cyber-border rounded-lg glass"
    >
      {/* Header */}
      <div className="text-center mb-8">
        <motion.div
          className="text-4xl mb-4"
          animate={{ 
            scale: [1, 1.1, 1],
            rotate: [0, 5, -5, 0]
          }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          ⚡
        </motion.div>
        <h1 className="text-2xl font-cyber font-bold text-neon-cyan">Join KodeKilat</h1>
        <p className="text-cyber-muted mt-2">Create your developer account</p>
      </div>

      {/* Error Message */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6 p-3 bg-red-500/10 border border-red-500/30 rounded-lg"
        >
          <div className="flex items-center space-x-2 text-red-400 text-sm">
            <Icon icon={AlertCircle} size={16} />
            <span>{error}</span>
          </div>
        </motion.div>
      )}

      {/* Registration Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Avatar Selection */}
        <div>
          <label className="block text-sm font-medium text-cyber-text mb-2">
            Choose Avatar
          </label>
          <div className="flex flex-wrap gap-2">
            {avatarOptions.map((avatar) => (
              <button
                key={avatar}
                type="button"
                onClick={() => handleChange('avatar', avatar)}
                className={`w-10 h-10 rounded-lg border-2 transition-all duration-200 ${
                  formData.avatar === avatar
                    ? 'border-neon-cyan bg-neon-cyan/20 scale-110'
                    : 'border-cyber-border hover:border-neon-cyan/50 hover:scale-105'
                }`}
                disabled={isLoading}
              >
                <span className="text-lg">{avatar}</span>
              </button>
            ))}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-cyber-text mb-2">
            Username
          </label>
          <div className="relative">
            <Icon 
              icon={User} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-cyber-muted" 
              size={16} 
            />
            <Input
              type="text"
              value={formData.username}
              onChange={(e) => handleChange('username', e.target.value)}
              className="pl-10"
              placeholder="Enter username"
              variant="cyber"
              required
              disabled={isLoading}
              minLength={3}
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-cyber-text mb-2">
            Email
          </label>
          <div className="relative">
            <Icon 
              icon={Mail} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-cyber-muted" 
              size={16} 
            />
            <Input
              type="email"
              value={formData.email}
              onChange={(e) => handleChange('email', e.target.value)}
              className="pl-10"
              placeholder="Enter your email"
              variant="cyber"
              required
              disabled={isLoading}
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-cyber-text mb-2">
            Password
          </label>
          <div className="relative">
            <Icon 
              icon={Lock} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-cyber-muted" 
              size={16} 
            />
            <Input
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={(e) => handleChange('password', e.target.value)}
              className="pl-10 pr-10"
              placeholder="Create password"
              variant="cyber"
              required
              disabled={isLoading}
              minLength={8}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-cyber-muted hover:text-neon-cyan transition-colors"
              disabled={isLoading}
            >
              <Icon icon={showPassword ? EyeOff : Eye} size={16} />
            </button>
          </div>
          {/* Password Strength Indicator */}
          {formData.password && (
            <div className="mt-2">
              <div className="flex items-center space-x-2">
                <div className="flex-1 h-1 bg-cyber-bg rounded-full overflow-hidden">
                  <div 
                    className={`h-full transition-all duration-300 ${
                      passwordStrength.strength === 1 ? 'w-1/3 bg-red-400' :
                      passwordStrength.strength === 2 ? 'w-2/3 bg-yellow-400' :
                      passwordStrength.strength === 3 ? 'w-full bg-green-400' : 'w-0'
                    }`}
                  />
                </div>
                <span className={`text-xs ${passwordStrength.color}`}>
                  {passwordStrength.label}
                </span>
              </div>
            </div>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-cyber-text mb-2">
            Confirm Password
          </label>
          <div className="relative">
            <Icon 
              icon={Lock} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-cyber-muted" 
              size={16} 
            />
            <Input
              type={showConfirmPassword ? 'text' : 'password'}
              value={formData.confirmPassword}
              onChange={(e) => handleChange('confirmPassword', e.target.value)}
              className="pl-10 pr-10"
              placeholder="Confirm password"
              variant="cyber"
              required
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-cyber-muted hover:text-neon-cyan transition-colors"
              disabled={isLoading}
            >
              <Icon icon={showConfirmPassword ? EyeOff : Eye} size={16} />
            </button>
          </div>
          {/* Password Match Indicator */}
          {formData.confirmPassword && (
            <div className="mt-2">
              {formData.password === formData.confirmPassword ? (
                <div className="flex items-center space-x-1 text-green-400 text-xs">
                  <Icon icon={CheckCircle} size={12} />
                  <span>Passwords match</span>
                </div>
              ) : (
                <div className="flex items-center space-x-1 text-red-400 text-xs">
                  <Icon icon={AlertCircle} size={12} />
                  <span>Passwords don't match</span>
                </div>
              )}
            </div>
          )}
        </div>

        <Button 
          type="submit" 
          variant="cyber" 
          className="w-full" 
          glow 
          disabled={isLoading || formData.password !== formData.confirmPassword}
        >
          {isLoading ? (
            <>
              <Icon icon={Loader2} size={16} className="mr-2 animate-spin" />
              Creating Account...
            </>
          ) : (
            <>
              <Icon icon={Sparkles} size={16} className="mr-2" />
              Create Account
            </>
          )}
        </Button>
      </form>

      {/* Footer */}
      <div className="mt-8 text-center">
        <div className="text-sm text-cyber-muted">
          Already have an account?{' '}
          <button
            onClick={onSwitchToLogin}
            className="text-neon-cyan hover:text-neon-cyan/80 transition-colors font-medium"
            disabled={isLoading}
          >
            Sign in
          </button>
        </div>

        {onClose && (
          <button
            onClick={onClose}
            className="text-sm text-cyber-muted hover:text-cyber-text transition-colors mt-4"
            disabled={isLoading}
          >
            Continue without account
          </button>
        )}
      </div>
    </motion.div>
  );
}
