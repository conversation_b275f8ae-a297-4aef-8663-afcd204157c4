{"version": 3, "file": "split.min.js", "sources": ["../src/split.js"], "sourcesContent": ["// The programming goals of Split.js are to deliver readable, understandable and\n// maintainable code, while at the same time manually optimizing for tiny minified file size,\n// browser compatibility without additional requirements\n// and very few assumptions about the user's page layout.\nconst global = typeof window !== 'undefined' ? window : null\nconst ssr = global === null\nconst document = !ssr ? global.document : undefined\n\n// Save a couple long function names that are used frequently.\n// This optimization saves around 400 bytes.\nconst addEventListener = 'addEventListener'\nconst removeEventListener = 'removeEventListener'\nconst getBoundingClientRect = 'getBoundingClientRect'\nconst gutterStartDragging = '_a'\nconst aGutterSize = '_b'\nconst bGutterSize = '_c'\nconst HORIZONTAL = 'horizontal'\nconst NOOP = () => false\n\n// Helper function determines which prefixes of CSS calc we need.\n// We only need to do this once on startup, when this anonymous function is called.\n//\n// Tests -webkit, -moz and -o prefixes. Modified from StackOverflow:\n// http://stackoverflow.com/questions/16625140/js-feature-detection-to-detect-the-usage-of-webkit-calc-over-calc/16625167#16625167\nconst calc = ssr\n    ? 'calc'\n    : `${['', '-webkit-', '-moz-', '-o-']\n          .filter(prefix => {\n              const el = document.createElement('div')\n              el.style.cssText = `width:${prefix}calc(9px)`\n\n              return !!el.style.length\n          })\n          .shift()}calc`\n\n// Helper function checks if its argument is a string-like type\nconst isString = v => typeof v === 'string' || v instanceof String\n\n// Helper function allows elements and string selectors to be used\n// interchangeably. In either case an element is returned. This allows us to\n// do `Split([elem1, elem2])` as well as `Split(['#id1', '#id2'])`.\nconst elementOrSelector = el => {\n    if (isString(el)) {\n        const ele = document.querySelector(el)\n        if (!ele) {\n            throw new Error(`Selector ${el} did not match a DOM element`)\n        }\n        return ele\n    }\n\n    return el\n}\n\n// Helper function gets a property from the properties object, with a default fallback\nconst getOption = (options, propName, def) => {\n    const value = options[propName]\n    if (value !== undefined) {\n        return value\n    }\n    return def\n}\n\nconst getGutterSize = (gutterSize, isFirst, isLast, gutterAlign) => {\n    if (isFirst) {\n        if (gutterAlign === 'end') {\n            return 0\n        }\n        if (gutterAlign === 'center') {\n            return gutterSize / 2\n        }\n    } else if (isLast) {\n        if (gutterAlign === 'start') {\n            return 0\n        }\n        if (gutterAlign === 'center') {\n            return gutterSize / 2\n        }\n    }\n\n    return gutterSize\n}\n\n// Default options\nconst defaultGutterFn = (i, gutterDirection) => {\n    const gut = document.createElement('div')\n    gut.className = `gutter gutter-${gutterDirection}`\n    return gut\n}\n\nconst defaultElementStyleFn = (dim, size, gutSize) => {\n    const style = {}\n\n    if (!isString(size)) {\n        style[dim] = `${calc}(${size}% - ${gutSize}px)`\n    } else {\n        style[dim] = size\n    }\n\n    return style\n}\n\nconst defaultGutterStyleFn = (dim, gutSize) => ({ [dim]: `${gutSize}px` })\n\n// The main function to initialize a split. Split.js thinks about each pair\n// of elements as an independant pair. Dragging the gutter between two elements\n// only changes the dimensions of elements in that pair. This is key to understanding\n// how the following functions operate, since each function is bound to a pair.\n//\n// A pair object is shaped like this:\n//\n// {\n//     a: DOM element,\n//     b: DOM element,\n//     aMin: Number,\n//     bMin: Number,\n//     dragging: Boolean,\n//     parent: DOM element,\n//     direction: 'horizontal' | 'vertical'\n// }\n//\n// The basic sequence:\n//\n// 1. Set defaults to something sane. `options` doesn't have to be passed at all.\n// 2. Initialize a bunch of strings based on the direction we're splitting.\n//    A lot of the behavior in the rest of the library is paramatized down to\n//    rely on CSS strings and classes.\n// 3. Define the dragging helper functions, and a few helpers to go with them.\n// 4. Loop through the elements while pairing them off. Every pair gets an\n//    `pair` object and a gutter.\n// 5. Actually size the pair elements, insert gutters and attach event listeners.\nconst Split = (idsOption, options = {}) => {\n    if (ssr) return {}\n\n    let ids = idsOption\n    let dimension\n    let clientAxis\n    let position\n    let positionEnd\n    let clientSize\n    let elements\n\n    // Allow HTMLCollection to be used as an argument when supported\n    if (Array.from) {\n        ids = Array.from(ids)\n    }\n\n    // All DOM elements in the split should have a common parent. We can grab\n    // the first elements parent and hope users read the docs because the\n    // behavior will be whacky otherwise.\n    const firstElement = elementOrSelector(ids[0])\n    const parent = firstElement.parentNode\n    const parentStyle = getComputedStyle ? getComputedStyle(parent) : null\n    const parentFlexDirection = parentStyle ? parentStyle.flexDirection : null\n\n    // Set default options.sizes to equal percentages of the parent element.\n    let sizes = getOption(options, 'sizes') || ids.map(() => 100 / ids.length)\n\n    // Standardize minSize and maxSize to an array if it isn't already.\n    // This allows minSize and maxSize to be passed as a number.\n    const minSize = getOption(options, 'minSize', 100)\n    const minSizes = Array.isArray(minSize) ? minSize : ids.map(() => minSize)\n    const maxSize = getOption(options, 'maxSize', Infinity)\n    const maxSizes = Array.isArray(maxSize) ? maxSize : ids.map(() => maxSize)\n\n    // Get other options\n    const expandToMin = getOption(options, 'expandToMin', false)\n    const gutterSize = getOption(options, 'gutterSize', 10)\n    const gutterAlign = getOption(options, 'gutterAlign', 'center')\n    const snapOffset = getOption(options, 'snapOffset', 30)\n    const snapOffsets = Array.isArray(snapOffset) ? snapOffset : ids.map(() => snapOffset)\n    const dragInterval = getOption(options, 'dragInterval', 1)\n    const direction = getOption(options, 'direction', HORIZONTAL)\n    const cursor = getOption(\n        options,\n        'cursor',\n        direction === HORIZONTAL ? 'col-resize' : 'row-resize',\n    )\n    const gutter = getOption(options, 'gutter', defaultGutterFn)\n    const elementStyle = getOption(\n        options,\n        'elementStyle',\n        defaultElementStyleFn,\n    )\n    const gutterStyle = getOption(options, 'gutterStyle', defaultGutterStyleFn)\n\n    // 2. Initialize a bunch of strings based on the direction we're splitting.\n    // A lot of the behavior in the rest of the library is paramatized down to\n    // rely on CSS strings and classes.\n    if (direction === HORIZONTAL) {\n        dimension = 'width'\n        clientAxis = 'clientX'\n        position = 'left'\n        positionEnd = 'right'\n        clientSize = 'clientWidth'\n    } else if (direction === 'vertical') {\n        dimension = 'height'\n        clientAxis = 'clientY'\n        position = 'top'\n        positionEnd = 'bottom'\n        clientSize = 'clientHeight'\n    }\n\n    // 3. Define the dragging helper functions, and a few helpers to go with them.\n    // Each helper is bound to a pair object that contains its metadata. This\n    // also makes it easy to store references to listeners that that will be\n    // added and removed.\n    //\n    // Even though there are no other functions contained in them, aliasing\n    // this to self saves 50 bytes or so since it's used so frequently.\n    //\n    // The pair object saves metadata like dragging state, position and\n    // event listener references.\n\n    function setElementSize(el, size, gutSize, i) {\n        // Split.js allows setting sizes via numbers (ideally), or if you must,\n        // by string, like '300px'. This is less than ideal, because it breaks\n        // the fluid layout that `calc(% - px)` provides. You're on your own if you do that,\n        // make sure you calculate the gutter size by hand.\n        const style = elementStyle(dimension, size, gutSize, i)\n\n        Object.keys(style).forEach(prop => {\n            // eslint-disable-next-line no-param-reassign\n            el.style[prop] = style[prop]\n        })\n    }\n\n    function setGutterSize(gutterElement, gutSize, i) {\n        const style = gutterStyle(dimension, gutSize, i)\n\n        Object.keys(style).forEach(prop => {\n            // eslint-disable-next-line no-param-reassign\n            gutterElement.style[prop] = style[prop]\n        })\n    }\n\n    function getSizes() {\n        return elements.map(element => element.size)\n    }\n\n    // Supports touch events, but not multitouch, so only the first\n    // finger `touches[0]` is counted.\n    function getMousePosition(e) {\n        if ('touches' in e) return e.touches[0][clientAxis]\n        return e[clientAxis]\n    }\n\n    // Actually adjust the size of elements `a` and `b` to `offset` while dragging.\n    // calc is used to allow calc(percentage + gutterpx) on the whole split instance,\n    // which allows the viewport to be resized without additional logic.\n    // Element a's size is the same as offset. b's size is total size - a size.\n    // Both sizes are calculated from the initial parent percentage,\n    // then the gutter size is subtracted.\n    function adjust(offset) {\n        const a = elements[this.a]\n        const b = elements[this.b]\n        const percentage = a.size + b.size\n\n        a.size = (offset / this.size) * percentage\n        b.size = percentage - (offset / this.size) * percentage\n\n        setElementSize(a.element, a.size, this[aGutterSize], a.i)\n        setElementSize(b.element, b.size, this[bGutterSize], b.i)\n    }\n\n    // drag, where all the magic happens. The logic is really quite simple:\n    //\n    // 1. Ignore if the pair is not dragging.\n    // 2. Get the offset of the event.\n    // 3. Snap offset to min if within snappable range (within min + snapOffset).\n    // 4. Actually adjust each element in the pair to offset.\n    //\n    // ---------------------------------------------------------------------\n    // |    | <- a.minSize               ||              b.minSize -> |    |\n    // |    |  | <- this.snapOffset      ||     this.snapOffset -> |  |    |\n    // |    |  |                         ||                        |  |    |\n    // |    |  |                         ||                        |  |    |\n    // ---------------------------------------------------------------------\n    // | <- this.start                                        this.size -> |\n    function drag(e) {\n        let offset\n        const a = elements[this.a]\n        const b = elements[this.b]\n\n        if (!this.dragging) return\n\n        // Get the offset of the event from the first side of the\n        // pair `this.start`. Then offset by the initial position of the\n        // mouse compared to the gutter size.\n        offset =\n            getMousePosition(e) -\n            this.start +\n            (this[aGutterSize] - this.dragOffset)\n\n        if (dragInterval > 1) {\n            offset = Math.round(offset / dragInterval) * dragInterval\n        }\n\n        // If within snapOffset of min or max, set offset to min or max.\n        // snapOffset buffers a.minSize and b.minSize, so logic is opposite for both.\n        // Include the appropriate gutter sizes to prevent overflows.\n        if (offset <= a.minSize + a.snapOffset + this[aGutterSize]) {\n            offset = a.minSize + this[aGutterSize]\n        } else if (\n            offset >=\n            this.size - (b.minSize + b.snapOffset + this[bGutterSize])\n        ) {\n            offset = this.size - (b.minSize + this[bGutterSize])\n        }\n\n        if (offset >= a.maxSize - a.snapOffset + this[aGutterSize]) {\n            offset = a.maxSize + this[aGutterSize]\n        } else if (\n            offset <=\n            this.size - (b.maxSize - b.snapOffset + this[bGutterSize])\n        ) {\n            offset = this.size - (b.maxSize + this[bGutterSize])\n        }\n\n        // Actually adjust the size.\n        adjust.call(this, offset)\n\n        // Call the drag callback continously. Don't do anything too intensive\n        // in this callback.\n        getOption(options, 'onDrag', NOOP)(getSizes())\n    }\n\n    // Cache some important sizes when drag starts, so we don't have to do that\n    // continously:\n    //\n    // `size`: The total size of the pair. First + second + first gutter + second gutter.\n    // `start`: The leading side of the first element.\n    //\n    // ------------------------------------------------\n    // |      aGutterSize -> |||                      |\n    // |                     |||                      |\n    // |                     |||                      |\n    // |                     ||| <- bGutterSize       |\n    // ------------------------------------------------\n    // | <- start                             size -> |\n    function calculateSizes() {\n        // Figure out the parent size minus padding.\n        const a = elements[this.a].element\n        const b = elements[this.b].element\n\n        const aBounds = a[getBoundingClientRect]()\n        const bBounds = b[getBoundingClientRect]()\n\n        this.size =\n            aBounds[dimension] +\n            bBounds[dimension] +\n            this[aGutterSize] +\n            this[bGutterSize]\n        this.start = aBounds[position]\n        this.end = aBounds[positionEnd]\n    }\n\n    function innerSize(element) {\n        // Return nothing if getComputedStyle is not supported (< IE9)\n        // Or if parent element has no layout yet\n        if (!getComputedStyle) return null\n\n        const computedStyle = getComputedStyle(element)\n\n        if (!computedStyle) return null\n\n        let size = element[clientSize]\n\n        if (size === 0) return null\n\n        if (direction === HORIZONTAL) {\n            size -=\n                parseFloat(computedStyle.paddingLeft) +\n                parseFloat(computedStyle.paddingRight)\n        } else {\n            size -=\n                parseFloat(computedStyle.paddingTop) +\n                parseFloat(computedStyle.paddingBottom)\n        }\n\n        return size\n    }\n\n    // When specifying percentage sizes that are less than the computed\n    // size of the element minus the gutter, the lesser percentages must be increased\n    // (and decreased from the other elements) to make space for the pixels\n    // subtracted by the gutters.\n    function trimToMin(sizesToTrim) {\n        // Try to get inner size of parent element.\n        // If it's no supported, return original sizes.\n        const parentSize = innerSize(parent)\n        if (parentSize === null) {\n            return sizesToTrim\n        }\n\n        if (minSizes.reduce((a, b) => a + b, 0) > parentSize) {\n            return sizesToTrim\n        }\n\n        // Keep track of the excess pixels, the amount of pixels over the desired percentage\n        // Also keep track of the elements with pixels to spare, to decrease after if needed\n        let excessPixels = 0\n        const toSpare = []\n\n        const pixelSizes = sizesToTrim.map((size, i) => {\n            // Convert requested percentages to pixel sizes\n            const pixelSize = (parentSize * size) / 100\n            const elementGutterSize = getGutterSize(\n                gutterSize,\n                i === 0,\n                i === sizesToTrim.length - 1,\n                gutterAlign,\n            )\n            const elementMinSize = minSizes[i] + elementGutterSize\n\n            // If element is too smal, increase excess pixels by the difference\n            // and mark that it has no pixels to spare\n            if (pixelSize < elementMinSize) {\n                excessPixels += elementMinSize - pixelSize\n                toSpare.push(0)\n                return elementMinSize\n            }\n\n            // Otherwise, mark the pixels it has to spare and return it's original size\n            toSpare.push(pixelSize - elementMinSize)\n            return pixelSize\n        })\n\n        // If nothing was adjusted, return the original sizes\n        if (excessPixels === 0) {\n            return sizesToTrim\n        }\n\n        return pixelSizes.map((pixelSize, i) => {\n            let newPixelSize = pixelSize\n\n            // While there's still pixels to take, and there's enough pixels to spare,\n            // take as many as possible up to the total excess pixels\n            if (excessPixels > 0 && toSpare[i] - excessPixels > 0) {\n                const takenPixels = Math.min(\n                    excessPixels,\n                    toSpare[i] - excessPixels,\n                )\n\n                // Subtract the amount taken for the next iteration\n                excessPixels -= takenPixels\n                newPixelSize = pixelSize - takenPixels\n            }\n\n            // Return the pixel size adjusted as a percentage\n            return (newPixelSize / parentSize) * 100\n        })\n    }\n\n    // stopDragging is very similar to startDragging in reverse.\n    function stopDragging() {\n        const self = this\n        const a = elements[self.a].element\n        const b = elements[self.b].element\n\n        if (self.dragging) {\n            getOption(options, 'onDragEnd', NOOP)(getSizes())\n        }\n\n        self.dragging = false\n\n        // Remove the stored event listeners. This is why we store them.\n        global[removeEventListener]('mouseup', self.stop)\n        global[removeEventListener]('touchend', self.stop)\n        global[removeEventListener]('touchcancel', self.stop)\n        global[removeEventListener]('mousemove', self.move)\n        global[removeEventListener]('touchmove', self.move)\n\n        // Clear bound function references\n        self.stop = null\n        self.move = null\n\n        a[removeEventListener]('selectstart', NOOP)\n        a[removeEventListener]('dragstart', NOOP)\n        b[removeEventListener]('selectstart', NOOP)\n        b[removeEventListener]('dragstart', NOOP)\n\n        a.style.userSelect = ''\n        a.style.webkitUserSelect = ''\n        a.style.MozUserSelect = ''\n        a.style.pointerEvents = ''\n\n        b.style.userSelect = ''\n        b.style.webkitUserSelect = ''\n        b.style.MozUserSelect = ''\n        b.style.pointerEvents = ''\n\n        self.gutter.style.cursor = ''\n        self.parent.style.cursor = ''\n        document.body.style.cursor = ''\n    }\n\n    // startDragging calls `calculateSizes` to store the inital size in the pair object.\n    // It also adds event listeners for mouse/touch events,\n    // and prevents selection while dragging so avoid the selecting text.\n    function startDragging(e) {\n        // Right-clicking can't start dragging.\n        if ('button' in e && e.button !== 0) {\n            return\n        }\n\n        // Alias frequently used variables to save space. 200 bytes.\n        const self = this\n        const a = elements[self.a].element\n        const b = elements[self.b].element\n\n        // Call the onDragStart callback.\n        if (!self.dragging) {\n            getOption(options, 'onDragStart', NOOP)(getSizes())\n        }\n\n        // Don't actually drag the element. We emulate that in the drag function.\n        e.preventDefault()\n\n        // Set the dragging property of the pair object.\n        self.dragging = true\n\n        // Create two event listeners bound to the same pair object and store\n        // them in the pair object.\n        self.move = drag.bind(self)\n        self.stop = stopDragging.bind(self)\n\n        // All the binding. `window` gets the stop events in case we drag out of the elements.\n        global[addEventListener]('mouseup', self.stop)\n        global[addEventListener]('touchend', self.stop)\n        global[addEventListener]('touchcancel', self.stop)\n        global[addEventListener]('mousemove', self.move)\n        global[addEventListener]('touchmove', self.move)\n\n        // Disable selection. Disable!\n        a[addEventListener]('selectstart', NOOP)\n        a[addEventListener]('dragstart', NOOP)\n        b[addEventListener]('selectstart', NOOP)\n        b[addEventListener]('dragstart', NOOP)\n\n        a.style.userSelect = 'none'\n        a.style.webkitUserSelect = 'none'\n        a.style.MozUserSelect = 'none'\n        a.style.pointerEvents = 'none'\n\n        b.style.userSelect = 'none'\n        b.style.webkitUserSelect = 'none'\n        b.style.MozUserSelect = 'none'\n        b.style.pointerEvents = 'none'\n\n        // Set the cursor at multiple levels\n        self.gutter.style.cursor = cursor\n        self.parent.style.cursor = cursor\n        document.body.style.cursor = cursor\n\n        // Cache the initial sizes of the pair.\n        calculateSizes.call(self)\n\n        // Determine the position of the mouse compared to the gutter\n        self.dragOffset = getMousePosition(e) - self.end\n    }\n\n    // adjust sizes to ensure percentage is within min size and gutter.\n    sizes = trimToMin(sizes)\n\n    // 5. Create pair and element objects. Each pair has an index reference to\n    // elements `a` and `b` of the pair (first and second elements).\n    // Loop through the elements while pairing them off. Every pair gets a\n    // `pair` object and a gutter.\n    //\n    // Basic logic:\n    //\n    // - Starting with the second element `i > 0`, create `pair` objects with\n    //   `a = i - 1` and `b = i`\n    // - Set gutter sizes based on the _pair_ being first/last. The first and last\n    //   pair have gutterSize / 2, since they only have one half gutter, and not two.\n    // - Create gutter elements and add event listeners.\n    // - Set the size of the elements, minus the gutter sizes.\n    //\n    // -----------------------------------------------------------------------\n    // |     i=0     |         i=1         |        i=2       |      i=3     |\n    // |             |                     |                  |              |\n    // |           pair 0                pair 1             pair 2           |\n    // |             |                     |                  |              |\n    // -----------------------------------------------------------------------\n    const pairs = []\n    elements = ids.map((id, i) => {\n        // Create the element object.\n        const element = {\n            element: elementOrSelector(id),\n            size: sizes[i],\n            minSize: minSizes[i],\n            maxSize: maxSizes[i],\n            snapOffset: snapOffsets[i],\n            i,\n        }\n\n        let pair\n\n        if (i > 0) {\n            // Create the pair object with its metadata.\n            pair = {\n                a: i - 1,\n                b: i,\n                dragging: false,\n                direction,\n                parent,\n            }\n\n            pair[aGutterSize] = getGutterSize(\n                gutterSize,\n                i - 1 === 0,\n                false,\n                gutterAlign,\n            )\n            pair[bGutterSize] = getGutterSize(\n                gutterSize,\n                false,\n                i === ids.length - 1,\n                gutterAlign,\n            )\n\n            // if the parent has a reverse flex-direction, switch the pair elements.\n            if (\n                parentFlexDirection === 'row-reverse' ||\n                parentFlexDirection === 'column-reverse'\n            ) {\n                const temp = pair.a\n                pair.a = pair.b\n                pair.b = temp\n            }\n        }\n\n        // Determine the size of the current element. IE8 is supported by\n        // staticly assigning sizes without draggable gutters. Assigns a string\n        // to `size`.\n        //\n        // Create gutter elements for each pair.\n        if (i > 0) {\n            const gutterElement = gutter(i, direction, element.element)\n            setGutterSize(gutterElement, gutterSize, i)\n\n            // Save bound event listener for removal later\n            pair[gutterStartDragging] = startDragging.bind(pair)\n\n            // Attach bound event listener\n            gutterElement[addEventListener](\n                'mousedown',\n                pair[gutterStartDragging],\n            )\n            gutterElement[addEventListener](\n                'touchstart',\n                pair[gutterStartDragging],\n            )\n\n            parent.insertBefore(gutterElement, element.element)\n\n            pair.gutter = gutterElement\n        }\n\n        setElementSize(\n            element.element,\n            element.size,\n            getGutterSize(\n                gutterSize,\n                i === 0,\n                i === ids.length - 1,\n                gutterAlign,\n            ),\n            i,\n        )\n\n        // After the first iteration, and we have a pair object, append it to the\n        // list of pairs.\n        if (i > 0) {\n            pairs.push(pair)\n        }\n\n        return element\n    })\n\n    function adjustToMin(element) {\n        const isLast = element.i === pairs.length\n        const pair = isLast ? pairs[element.i - 1] : pairs[element.i]\n\n        calculateSizes.call(pair)\n\n        const size = isLast\n            ? pair.size - element.minSize - pair[bGutterSize]\n            : element.minSize + pair[aGutterSize]\n\n        adjust.call(pair, size)\n    }\n\n    elements.forEach(element => {\n        const computedSize = element.element[getBoundingClientRect]()[dimension]\n\n        if (computedSize < element.minSize) {\n            if (expandToMin) {\n                adjustToMin(element)\n            } else {\n                // eslint-disable-next-line no-param-reassign\n                element.minSize = computedSize\n            }\n        }\n    })\n\n    function setSizes(newSizes) {\n        const trimmed = trimToMin(newSizes)\n        trimmed.forEach((newSize, i) => {\n            if (i > 0) {\n                const pair = pairs[i - 1]\n\n                const a = elements[pair.a]\n                const b = elements[pair.b]\n\n                a.size = trimmed[i - 1]\n                b.size = newSize\n\n                setElementSize(a.element, a.size, pair[aGutterSize], a.i)\n                setElementSize(b.element, b.size, pair[bGutterSize], b.i)\n            }\n        })\n    }\n\n    function destroy(preserveStyles, preserveGutter) {\n        pairs.forEach(pair => {\n            if (preserveGutter !== true) {\n                pair.parent.removeChild(pair.gutter)\n            } else {\n                pair.gutter[removeEventListener](\n                    'mousedown',\n                    pair[gutterStartDragging],\n                )\n                pair.gutter[removeEventListener](\n                    'touchstart',\n                    pair[gutterStartDragging],\n                )\n            }\n\n            if (preserveStyles !== true) {\n                const style = elementStyle(\n                    dimension,\n                    pair.a.size,\n                    pair[aGutterSize],\n                )\n\n                Object.keys(style).forEach(prop => {\n                    elements[pair.a].element.style[prop] = ''\n                    elements[pair.b].element.style[prop] = ''\n                })\n            }\n        })\n    }\n\n    return {\n        setSizes,\n        getSizes,\n        collapse(i) {\n            adjustToMin(elements[i])\n        },\n        destroy,\n        parent,\n        pairs,\n    }\n}\n\nexport default Split\n"], "names": ["const", "global", "window", "ssr", "document", "undefined", "NOOP", "calc", "filter", "prefix", "el", "createElement", "style", "cssText", "length", "shift", "isString", "v", "String", "elementOrSelector", "ele", "querySelector", "Error", "getOption", "options", "propName", "def", "value", "getGutterSize", "gutterSize", "<PERSON><PERSON><PERSON><PERSON>", "isLast", "gutterAlign", "defaultGutterFn", "i", "gutterDirection", "gut", "className", "defaultElementStyleFn", "dim", "size", "gutSize", "defaultGutterStyleFn", "idsOption", "let", "dimension", "clientAxis", "position", "positionEnd", "clientSize", "elements", "ids", "Array", "from", "parent", "parentNode", "parentStyle", "getComputedStyle", "parentFlexDirection", "flexDirection", "sizes", "map", "minSize", "minSizes", "isArray", "maxSize", "Infinity", "maxSizes", "expandToMin", "snapOffset", "snapOffsets", "dragInterval", "direction", "cursor", "gutter", "elementStyle", "gutterStyle", "setElementSize", "Object", "keys", "for<PERSON>ach", "prop", "getSizes", "element", "getMousePosition", "e", "touches", "adjust", "offset", "a", "this", "b", "percentage", "drag", "dragging", "start", "dragOffset", "Math", "round", "call", "calculateSizes", "aBounds", "bBounds", "end", "trimToMin", "sizesToTrim", "parentSize", "computedStyle", "parseFloat", "paddingLeft", "paddingRight", "paddingTop", "paddingBottom", "innerSize", "reduce", "excessPixels", "to<PERSON><PERSON><PERSON>", "pixelSizes", "pixelSize", "elementGutterSize", "elementMinSize", "push", "newPixelSize", "takenPixels", "min", "stopDragging", "stop", "move", "userSelect", "webkitUserSelect", "MozUserSelect", "pointerEvents", "body", "startDragging", "button", "preventDefault", "bind", "pairs", "adjustToMin", "pair", "id", "temp", "gutterElement", "setGutterSize", "insertBefore", "computedSize", "newSizes", "trimmed", "newSize", "collapse", "preserveStyles", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";sOAIAA,IAAMC,EAA2B,oBAAXC,OAAyBA,OAAS,KAClDC,EAAiB,OAAXF,EACNG,EAAYD,OAAwBE,EAAlBJ,EAAOG,SAWzBE,oBAAa,GAObC,EAAOJ,EACP,OACG,CAAC,GAAI,WAAY,QAAS,OACxBK,iBAAOC,GACJT,IAAMU,EAAKN,EAASO,cAAc,OAGlC,OAFAD,EAAGE,MAAMC,QAAU,SAASJ,gBAEnBC,EAAGE,MAAME,UAErBC,eAGLC,WAAWC,SAAkB,iBAANA,GAAkBA,aAAaC,QAKtDC,WAAoBT,GACtB,GAAIM,EAASN,GAAK,CACdV,IAAMoB,EAAMhB,EAASiB,cAAcX,GACnC,IAAKU,EACD,MAAM,IAAIE,kBAAkBZ,kCAEhC,OAAOU,EAGX,OAAOV,GAILa,WAAaC,EAASC,EAAUC,GAClC1B,IAAM2B,EAAQH,EAAQC,GACtB,YAAcpB,IAAVsB,EACOA,EAEJD,GAGLE,WAAiBC,EAAYC,EAASC,EAAQC,GAChD,GAAIF,EAAS,CACT,GAAoB,QAAhBE,EACA,OAAO,EAEX,GAAoB,WAAhBA,EACA,OAAOH,EAAa,OAErB,GAAIE,EAAQ,CACf,GAAoB,UAAhBC,EACA,OAAO,EAEX,GAAoB,WAAhBA,EACA,OAAOH,EAAa,EAI5B,OAAOA,GAILI,WAAmBC,EAAGC,GACxBnC,IAAMoC,EAAMhC,EAASO,cAAc,OAEnC,OADAyB,EAAIC,UAAY,iBAAiBF,EAC1BC,GAGLE,WAAyBC,EAAKC,EAAMC,GACtCzC,IAAMY,EAAQ,GAQd,OANKI,EAASwB,GAGV5B,EAAM2B,GAAOC,EAFb5B,EAAM2B,GAAUhC,MAAQiC,SAAWC,QAKhC7B,GAGL8B,WAAwBH,EAAKE,kBAAa,IAAGF,GAASE,0BA6B7CE,EAAWnB,GACtB,kBADgC,IAC5BrB,EAAK,MAAO,GAEhByC,IACIC,EACAC,EACAC,EACAC,EACAC,EACAC,EANAC,EAAMR,EASNS,MAAMC,OACNF,EAAMC,MAAMC,KAAKF,IAMrBnD,IACMsD,EADenC,EAAkBgC,EAAI,IACfI,WACtBC,EAAcC,iBAAmBA,iBAAiBH,GAAU,KAC5DI,EAAsBF,EAAcA,EAAYG,cAAgB,KAGlEC,EAAQrC,EAAUC,EAAS,UAAY2B,EAAIU,uBAAU,IAAMV,EAAIrC,UAI7DgD,EAAUvC,EAAUC,EAAS,UAAW,KACxCuC,EAAWX,MAAMY,QAAQF,GAAWA,EAAUX,EAAIU,uBAAUC,KAC5DG,EAAU1C,EAAUC,EAAS,UAAW0C,EAAAA,GACxCC,EAAWf,MAAMY,QAAQC,GAAWA,EAAUd,EAAIU,uBAAUI,KAG5DG,EAAc7C,EAAUC,EAAS,eAAe,GAChDK,EAAaN,EAAUC,EAAS,aAAc,IAC9CQ,EAAcT,EAAUC,EAAS,cAAe,UAChD6C,EAAa9C,EAAUC,EAAS,aAAc,IAC9C8C,EAAclB,MAAMY,QAAQK,GAAcA,EAAalB,EAAIU,uBAAUQ,KACrEE,EAAehD,EAAUC,EAAS,eAAgB,GAClDgD,EAAYjD,EAAUC,EAAS,YA3JtB,cA4JTiD,EAASlD,EACXC,EACA,SA9JW,eA+JXgD,EAA2B,aAAe,cAExCE,EAASnD,EAAUC,EAAS,SAAUS,GACtC0C,EAAepD,EACjBC,EACA,eACAc,GAEEsC,EAAcrD,EAAUC,EAAS,cAAekB,GA8BtD,SAASmC,EAAenE,EAAI8B,EAAMC,EAASP,GAKvClC,IAAMY,EAAQ+D,EAAa9B,EAAWL,EAAMC,EAASP,GAErD4C,OAAOC,KAAKnE,GAAOoE,kBAAQC,GAEvBvE,EAAGE,MAAMqE,GAAQrE,EAAMqE,MAa/B,SAASC,IACL,OAAOhC,EAASW,cAAIsB,UAAWA,EAAQ3C,QAK3C,SAAS4C,EAAiBC,GACtB,MAAI,YAAaA,EAAUA,EAAEC,QAAQ,GAAGxC,GACjCuC,EAAEvC,GASb,SAASyC,EAAOC,GACZxF,IAAMyF,EAAIvC,EAASwC,KAAKD,GAClBE,EAAIzC,EAASwC,KAAKC,GAClBC,EAAaH,EAAEjD,KAAOmD,EAAEnD,KAE9BiD,EAAEjD,KAAQgD,EAASE,KAAKlD,KAAQoD,EAChCD,EAAEnD,KAAOoD,EAAcJ,EAASE,KAAKlD,KAAQoD,EAE7Cf,EAAeY,EAAEN,QAASM,EAAEjD,KAAMkD,KAAgB,GAAGD,EAAEvD,GACvD2C,EAAec,EAAER,QAASQ,EAAEnD,KAAMkD,KAAgB,GAAGC,EAAEzD,GAiB3D,SAAS2D,EAAKR,GACVzC,IAAI4C,EACEC,EAAIvC,EAASwC,KAAKD,GAClBE,EAAIzC,EAASwC,KAAKC,GAEnBD,KAAKI,WAKVN,EACIJ,EAAiBC,GACjBK,KAAKK,OACJL,KAAgB,GAAIA,KAAKM,YAE1BzB,EAAe,IACfiB,EAASS,KAAKC,MAAMV,EAASjB,GAAgBA,GAM7CiB,GAAUC,EAAE3B,QAAU2B,EAAEpB,WAAaqB,KAAgB,GACrDF,EAASC,EAAE3B,QAAU4B,KAAgB,GAErCF,GACAE,KAAKlD,MAAQmD,EAAE7B,QAAU6B,EAAEtB,WAAaqB,KAAgB,MAExDF,EAASE,KAAKlD,MAAQmD,EAAE7B,QAAU4B,KAAgB,KAGlDF,GAAUC,EAAExB,QAAUwB,EAAEpB,WAAaqB,KAAgB,GACrDF,EAASC,EAAExB,QAAUyB,KAAgB,GAErCF,GACAE,KAAKlD,MAAQmD,EAAE1B,QAAU0B,EAAEtB,WAAaqB,KAAgB,MAExDF,EAASE,KAAKlD,MAAQmD,EAAE1B,QAAUyB,KAAgB,KAItDH,EAAOY,KAAKT,KAAMF,GAIlBjE,EAAUC,EAAS,SAAUlB,EAA7BiB,CAAmC2D,MAgBvC,SAASkB,IAELpG,IAAMyF,EAAIvC,EAASwC,KAAKD,GAAGN,QACrBQ,EAAIzC,EAASwC,KAAKC,GAAGR,QAErBkB,EAAUZ,EAAuB,wBACjCa,EAAUX,EAAuB,wBAEvCD,KAAKlD,KACD6D,EAAQxD,GACRyD,EAAQzD,GACR6C,KAAgB,GAChBA,KAAgB,GACpBA,KAAKK,MAAQM,EAAQtD,GACrB2C,KAAKa,IAAMF,EAAQrD,GAiCvB,SAASwD,EAAUC,GAGfzG,IAAM0G,EAjCV,SAAmBvB,GAGf,IAAK1B,iBAAkB,OAAO,KAE9BzD,IAAM2G,EAAgBlD,iBAAiB0B,GAEvC,IAAKwB,EAAe,OAAO,KAE3B/D,IAAIJ,EAAO2C,EAAQlC,GAEnB,OAAa,IAATT,EAAmB,KAGnBA,GAlWO,eAiWPgC,EAEIoC,WAAWD,EAAcE,aACzBD,WAAWD,EAAcG,cAGzBF,WAAWD,EAAcI,YACzBH,WAAWD,EAAcK,eAadC,CAAU3D,GAC7B,GAAmB,OAAfoD,EACA,OAAOD,EAGX,GAAI1C,EAASmD,iBAAQzB,EAAGE,UAAMF,EAAIE,IAAG,GAAKe,EACtC,OAAOD,EAKX7D,IAAIuE,EAAe,EACbC,EAAU,GAEVC,EAAaZ,EAAY5C,cAAKrB,EAAMN,GAEtClC,IAAMsH,EAAaZ,EAAalE,EAAQ,IAClC+E,EAAoB3F,EACtBC,EACM,IAANK,EACAA,IAAMuE,EAAY3F,OAAS,EAC3BkB,GAEEwF,EAAiBzD,EAAS7B,GAAKqF,EAIrC,OAAID,EAAYE,GACZL,GAAgBK,EAAiBF,EACjCF,EAAQK,KAAK,GACND,IAIXJ,EAAQK,KAAKH,EAAYE,GAClBF,MAIX,OAAqB,IAAjBH,EACOV,EAGJY,EAAWxD,cAAKyD,EAAWpF,GAC9BU,IAAI8E,EAAeJ,EAInB,GAAIH,EAAe,GAAKC,EAAQlF,GAAKiF,EAAe,EAAG,CACnDnH,IAAM2H,EAAc1B,KAAK2B,IACrBT,EACAC,EAAQlF,GAAKiF,GAIjBA,GAAgBQ,EAChBD,EAAeJ,EAAYK,EAI/B,OAAQD,EAAehB,EAAc,OAK7C,SAASmB,IACL7H,IACMyF,EAAIvC,EADGwC,KACWD,GAAGN,QACrBQ,EAAIzC,EAFGwC,KAEWC,GAAGR,QAFdO,KAIJI,UACLvE,EAAUC,EAAS,YAAalB,EAAhCiB,CAAsC2D,KAL7BQ,KAQRI,UAAW,EAGhB7F,EAA0B,oBAAE,UAXfyF,KAW+BoC,MAC5C7H,EAA0B,oBAAE,WAZfyF,KAYgCoC,MAC7C7H,EAA0B,oBAAE,cAbfyF,KAamCoC,MAChD7H,EAA0B,oBAAE,YAdfyF,KAciCqC,MAC9C9H,EAA0B,oBAAE,YAffyF,KAeiCqC,MAfjCrC,KAkBRoC,KAAO,KAlBCpC,KAmBRqC,KAAO,KAEZtC,EAAqB,oBAAE,cAAenF,GACtCmF,EAAqB,oBAAE,YAAanF,GACpCqF,EAAqB,oBAAE,cAAerF,GACtCqF,EAAqB,oBAAE,YAAarF,GAEpCmF,EAAE7E,MAAMoH,WAAa,GACrBvC,EAAE7E,MAAMqH,iBAAmB,GAC3BxC,EAAE7E,MAAMsH,cAAgB,GACxBzC,EAAE7E,MAAMuH,cAAgB,GAExBxC,EAAE/E,MAAMoH,WAAa,GACrBrC,EAAE/E,MAAMqH,iBAAmB,GAC3BtC,EAAE/E,MAAMsH,cAAgB,GACxBvC,EAAE/E,MAAMuH,cAAgB,GAlCXzC,KAoCRhB,OAAO9D,MAAM6D,OAAS,GApCdiB,KAqCRpC,OAAO1C,MAAM6D,OAAS,GAC3BrE,EAASgI,KAAKxH,MAAM6D,OAAS,GAMjC,SAAS4D,EAAchD,GAEnB,KAAI,WAAYA,IAAkB,IAAbA,EAAEiD,OAAvB,CAKAtI,IACMyF,EAAIvC,EADGwC,KACWD,GAAGN,QACrBQ,EAAIzC,EAFGwC,KAEWC,GAAGR,QAFdO,KAKHI,UACNvE,EAAUC,EAAS,cAAelB,EAAlCiB,CAAwC2D,KAI5CG,EAAEkD,iBAVW7C,KAaRI,UAAW,EAbHJ,KAiBRqC,KAAOlC,EAAK2C,KAjBJ9C,MAAAA,KAkBRoC,KAAOD,EAAaW,KAlBZ9C,MAqBbzF,EAAuB,iBAAE,UArBZyF,KAqB4BoC,MACzC7H,EAAuB,iBAAE,WAtBZyF,KAsB6BoC,MAC1C7H,EAAuB,iBAAE,cAvBZyF,KAuBgCoC,MAC7C7H,EAAuB,iBAAE,YAxBZyF,KAwB8BqC,MAC3C9H,EAAuB,iBAAE,YAzBZyF,KAyB8BqC,MAG3CtC,EAAkB,iBAAE,cAAenF,GACnCmF,EAAkB,iBAAE,YAAanF,GACjCqF,EAAkB,iBAAE,cAAerF,GACnCqF,EAAkB,iBAAE,YAAarF,GAEjCmF,EAAE7E,MAAMoH,WAAa,OACrBvC,EAAE7E,MAAMqH,iBAAmB,OAC3BxC,EAAE7E,MAAMsH,cAAgB,OACxBzC,EAAE7E,MAAMuH,cAAgB,OAExBxC,EAAE/E,MAAMoH,WAAa,OACrBrC,EAAE/E,MAAMqH,iBAAmB,OAC3BtC,EAAE/E,MAAMsH,cAAgB,OACxBvC,EAAE/E,MAAMuH,cAAgB,OAzCXzC,KA4CRhB,OAAO9D,MAAM6D,OAASA,EA5CdiB,KA6CRpC,OAAO1C,MAAM6D,OAASA,EAC3BrE,EAASgI,KAAKxH,MAAM6D,OAASA,EAG7B2B,EAAeD,KAjDFT,MAAAA,KAoDRM,WAAaZ,EAAiBC,GApDtBK,KAoDgCa,KA9hBlC,eA4KX/B,GACA3B,EAAY,QACZC,EAAa,UACbC,EAAW,OACXC,EAAc,QACdC,EAAa,eACQ,aAAduB,IACP3B,EAAY,SACZC,EAAa,UACbC,EAAW,MACXC,EAAc,SACdC,EAAa,gBA2WjBW,EAAQ4C,EAAU5C,GAsBlB5D,IAAMyI,EAAQ,GAgGd,SAASC,EAAYvD,GACjBnF,IAAM+B,EAASoD,EAAQjD,IAAMuG,EAAM3H,OAC7B6H,EAAO5G,EAAS0G,EAAMtD,EAAQjD,EAAI,GAAKuG,EAAMtD,EAAQjD,GAE3DkE,EAAeD,KAAKwC,GAEpB3I,IAAMwC,EAAOT,EACP4G,EAAKnG,KAAO2C,EAAQrB,QAAU6E,EAAgB,GAC9CxD,EAAQrB,QAAU6E,EAAgB,GAExCpD,EAAOY,KAAKwC,EAAMnG,GAgEtB,OAzKAU,EAAWC,EAAIU,cAAK+E,EAAI1G,GAEpBlC,IASI2I,EATExD,EAAU,CACZA,QAAShE,EAAkByH,GAC3BpG,KAAMoB,EAAM1B,GACZ4B,QAASC,EAAS7B,GAClB+B,QAASE,EAASjC,GAClBmC,WAAYC,EAAYpC,KACxBA,GAKJ,GAAIA,EAAI,KAEJyG,EAAO,CACHlD,EAAGvD,EAAI,EACPyD,EAAGzD,EACH4D,UAAU,YACVtB,SACAlB,IAGY,GAAI1B,EAChBC,EACAK,EAAI,GAAM,GACV,EACAF,GAEJ2G,EAAgB,GAAI/G,EAChBC,GACA,EACAK,IAAMiB,EAAIrC,OAAS,EACnBkB,GAKwB,gBAAxB0B,GACwB,mBAAxBA,GACF,CACE1D,IAAM6I,EAAOF,EAAKlD,EAClBkD,EAAKlD,EAAIkD,EAAKhD,EACdgD,EAAKhD,EAAIkD,EASjB,GAAI3G,EAAI,EAAG,CACPlC,IAAM8I,EAAgBpE,EAAOxC,EAAGsC,EAAWW,EAAQA,UA5Z3D,SAAuB2D,EAAerG,EAASP,GAC3ClC,IAAMY,EAAQgE,EAAY/B,EAAWJ,EAASP,GAE9C4C,OAAOC,KAAKnE,GAAOoE,kBAAQC,GAEvB6D,EAAclI,MAAMqE,GAAQrE,EAAMqE,MAwZlC8D,CAAcD,EAAejH,EAAYK,GAGzCyG,EAAwB,GAAIN,EAAcG,KAAKG,GAG/CG,EAA8B,iBAC1B,YACAH,EAAwB,IAE5BG,EAA8B,iBAC1B,aACAH,EAAwB,IAG5BrF,EAAO0F,aAAaF,EAAe3D,EAAQA,SAE3CwD,EAAKjE,OAASoE,EAqBlB,OAlBAjE,EACIM,EAAQA,QACRA,EAAQ3C,KACRZ,EACIC,EACM,IAANK,EACAA,IAAMiB,EAAIrC,OAAS,EACnBkB,GAEJE,GAKAA,EAAI,GACJuG,EAAMhB,KAAKkB,GAGRxD,MAgBFH,kBAAQG,GACbnF,IAAMiJ,EAAe9D,EAAQA,QAA6B,wBAAItC,GAE1DoG,EAAe9D,EAAQrB,UACnBM,EACAsE,EAAYvD,GAGZA,EAAQrB,QAAUmF,MAqDvB,UAhDP,SAAkBC,GACdlJ,IAAMmJ,EAAU3C,EAAU0C,GAC1BC,EAAQnE,kBAASoE,EAASlH,GACtB,GAAIA,EAAI,EAAG,CACPlC,IAAM2I,EAAOF,EAAMvG,EAAI,GAEjBuD,EAAIvC,EAASyF,EAAKlD,GAClBE,EAAIzC,EAASyF,EAAKhD,GAExBF,EAAEjD,KAAO2G,EAAQjH,EAAI,GACrByD,EAAEnD,KAAO4G,EAETvE,EAAeY,EAAEN,QAASM,EAAEjD,KAAMmG,EAAgB,GAAGlD,EAAEvD,GACvD2C,EAAec,EAAER,QAASQ,EAAEnD,KAAMmG,EAAgB,GAAGhD,EAAEzD,iBAqC/DgD,EACAmE,kBAASnH,GACLwG,EAAYxF,EAAShB,aAlC7B,SAAiBoH,EAAgBC,GAC7Bd,EAAMzD,kBAAQ2D,GAcV,IAbuB,IAAnBY,EACAZ,EAAKrF,OAAOkG,YAAYb,EAAKjE,SAE7BiE,EAAKjE,OAA0B,oBAC3B,YACAiE,EAAwB,IAE5BA,EAAKjE,OAA0B,oBAC3B,aACAiE,EAAwB,MAIT,IAAnBW,EAAyB,CACzBtJ,IAAMY,EAAQ+D,EACV9B,EACA8F,EAAKlD,EAAEjD,KACPmG,EAAgB,IAGpB7D,OAAOC,KAAKnE,GAAOoE,kBAAQC,GACvB/B,EAASyF,EAAKlD,GAAGN,QAAQvE,MAAMqE,GAAQ,GACvC/B,EAASyF,EAAKhD,GAAGR,QAAQvE,MAAMqE,GAAQ,kBAanD3B,QACAmF"}