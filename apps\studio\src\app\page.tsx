'use client';

import { useEffect, useState } from 'react';
import { Workbench } from '../components/layout/workbench';
import { SplashScreen } from '../components/layout/splash-screen';
import { WelcomeScreen } from '../components/layout/welcome-screen';
import { useAppStore } from '../stores/app-store';

export default function HomePage() {
  const [isLoading, setIsLoading] = useState(true);
  const { user, workspace, initializeApp } = useAppStore();

  useEffect(() => {
    const init = async () => {
      try {
        await initializeApp();
      } catch (error) {
        console.error('Failed to initialize app:', error);
      } finally {
        // Simulate loading time for splash screen
        setTimeout(() => {
          setIsLoading(false);
        }, 2000);
      }
    };

    init();
  }, [initializeApp]);

  if (isLoading) {
    return <SplashScreen />;
  }

  // Show welcome screen if no user or workspace
  if (!user || !workspace) {
    return <WelcomeScreen />;
  }

  // Show main workbench
  return <Workbench />;
}
