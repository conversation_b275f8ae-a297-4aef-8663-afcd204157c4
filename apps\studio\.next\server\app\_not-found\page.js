/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2F..%2F..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CUsers%5CDownloads%5Ckodekilat-newsss%5Capps%5Cstudio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5Ckodekilat-newsss%5Capps%5Cstudio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2F..%2F..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CUsers%5CDownloads%5Ckodekilat-newsss%5Capps%5Cstudio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5Ckodekilat-newsss%5Capps%5Cstudio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1c3a\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodekilat-newsss\\\\apps\\\\studio\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2F..%2F..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CUsers%5CDownloads%5Ckodekilat-newsss%5Capps%5Cstudio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5Ckodekilat-newsss%5Capps%5Cstudio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Capps%5C%5Cstudio%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Capps%5C%5Cstudio%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDVXNlcnMlNUMlNUNEb3dubG9hZHMlNUMlNUNrb2Rla2lsYXQtbmV3c3NzJTVDJTVDYXBwcyU1QyU1Q3N0dWRpbyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUErRyIsInNvdXJjZXMiOlsid2VicGFjazovL0Brb2Rla2lsYXQvc3R1ZGlvLz82MDc1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVraWxhdC1uZXdzc3NcXFxcYXBwc1xcXFxzdHVkaW9cXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Capps%5C%5Cstudio%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5Ckodekilat-newsss%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(ssr)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(ssr)/./src/styles/globals.css\");\n/* harmony import */ var _components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/theme-provider */ \"(ssr)/./src/components/providers/theme-provider.tsx\");\n/* harmony import */ var _components_providers_store_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/store-provider */ \"(ssr)/./src/components/providers/store-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"KodeKilat Studio ⚡\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodekilat-newsss\\\\apps\\\\studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"IDE Desktop Lokal Nusantara dengan AI dan UI Futuristik\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodekilat-newsss\\\\apps\\\\studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodekilat-newsss\\\\apps\\\\studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodekilat-newsss\\\\apps\\\\studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodekilat-newsss\\\\apps\\\\studio\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} theme-cyberpunk`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_store_provider__WEBPACK_IMPORTED_MODULE_3__.StoreProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodekilat-newsss\\\\apps\\\\studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodekilat-newsss\\\\apps\\\\studio\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodekilat-newsss\\\\apps\\\\studio\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodekilat-newsss\\\\apps\\\\studio\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/store-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/store-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StoreProvider: () => (/* binding */ StoreProvider),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_app_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/app-store */ \"(ssr)/./src/stores/app-store.ts\");\n/* __next_internal_client_entry_do_not_use__ StoreProvider,useStore auto */ \n\n\nconst StoreContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction StoreProvider({ children }) {\n    const initializeApp = (0,_stores_app_store__WEBPACK_IMPORTED_MODULE_2__.useAppStore)((state)=>state.initializeApp);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize app on mount\n        initializeApp();\n    }, [\n        initializeApp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StoreContext.Provider, {\n        value: {},\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodekilat-newsss\\\\apps\\\\studio\\\\src\\\\components\\\\providers\\\\store-provider.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\nfunction useStore() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(StoreContext);\n    if (context === undefined) {\n        throw new Error(\"useStore must be used within a StoreProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/store-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/theme-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/theme-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_app_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/app-store */ \"(ssr)/./src/stores/app-store.ts\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const { theme, setTheme } = (0,_stores_app_store__WEBPACK_IMPORTED_MODULE_2__.useAppStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Apply theme to document\n        const root = document.documentElement;\n        // Remove existing theme classes\n        root.classList.remove(\"theme-cyberpunk\", \"theme-matrix\", \"theme-classic\");\n        // Add current theme class\n        root.classList.add(`theme-${theme.type}`);\n        // Update CSS custom properties\n        const style = root.style;\n        Object.entries(theme.colors).forEach(([key, value])=>{\n            const cssVar = `--${key.replace(/([A-Z])/g, \"-$1\").toLowerCase()}`;\n            style.setProperty(cssVar, value);\n        });\n    }, [\n        theme\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            setTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodekilat-newsss\\\\apps\\\\studio\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/services/auth.ts":
/*!******************************!*\
  !*** ./src/services/auth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService)\n/* harmony export */ });\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database */ \"(ssr)/./src/services/database.ts\");\n// KodeKilat Studio ⚡ - Authentication Service\n\nclass AuthService {\n    static getInstance() {\n        if (!AuthService.instance) {\n            AuthService.instance = new AuthService();\n        }\n        return AuthService.instance;\n    }\n    async login(credentials) {\n        try {\n            // Validate input\n            if (!credentials.email || !credentials.password) {\n                return {\n                    success: false,\n                    error: \"Email and password are required\"\n                };\n            }\n            if (!this.isValidEmail(credentials.email)) {\n                return {\n                    success: false,\n                    error: \"Invalid email format\"\n                };\n            }\n            // Authenticate user\n            const user = await _database__WEBPACK_IMPORTED_MODULE_0__.databaseService.authenticateUser(credentials.email, credentials.password);\n            if (!user) {\n                return {\n                    success: false,\n                    error: \"Invalid email or password\"\n                };\n            }\n            // Create session\n            const session = await _database__WEBPACK_IMPORTED_MODULE_0__.databaseService.createSession(user.id);\n            this.currentSession = session;\n            // Store session token\n            localStorage.setItem(\"kodekilat-session-token\", session.token);\n            return {\n                success: true,\n                user\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: \"Login failed. Please try again.\"\n            };\n        }\n    }\n    async register(data) {\n        try {\n            // Validate input\n            const validation = this.validateRegistrationData(data);\n            if (!validation.isValid) {\n                return {\n                    success: false,\n                    error: validation.error\n                };\n            }\n            // Check if user already exists\n            const existingUser = await _database__WEBPACK_IMPORTED_MODULE_0__.databaseService.getUserByEmail(data.email);\n            if (existingUser) {\n                return {\n                    success: false,\n                    error: \"User with this email already exists\"\n                };\n            }\n            // Create user\n            const user = await _database__WEBPACK_IMPORTED_MODULE_0__.databaseService.createUser({\n                username: data.username,\n                email: data.email,\n                password: data.password,\n                avatar: data.avatar\n            });\n            // Auto-login after registration\n            const session = await _database__WEBPACK_IMPORTED_MODULE_0__.databaseService.createSession(user.id);\n            this.currentSession = session;\n            // Store session token\n            localStorage.setItem(\"kodekilat-session-token\", session.token);\n            return {\n                success: true,\n                user\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                error: \"Registration failed. Please try again.\"\n            };\n        }\n    }\n    async logout() {\n        try {\n            // Invalidate current session\n            if (this.currentSession) {\n                await _database__WEBPACK_IMPORTED_MODULE_0__.databaseService.invalidateSession(this.currentSession.token);\n                this.currentSession = null;\n            }\n            // Clear stored session token\n            localStorage.removeItem(\"kodekilat-session-token\");\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    }\n    async getCurrentUser() {\n        try {\n            const token = localStorage.getItem(\"kodekilat-session-token\");\n            if (!token) {\n                return null;\n            }\n            const session = await _database__WEBPACK_IMPORTED_MODULE_0__.databaseService.getSessionByToken(token);\n            if (!session) {\n                // Session expired or invalid\n                localStorage.removeItem(\"kodekilat-session-token\");\n                return null;\n            }\n            this.currentSession = session;\n            // Get user data\n            const users = JSON.parse(localStorage.getItem(\"kodekilat-users\") || \"[]\");\n            const userData = users.find((u)=>u.id === session.userId);\n            if (!userData) {\n                return null;\n            }\n            return {\n                id: userData.id,\n                username: userData.username,\n                email: userData.email,\n                avatar: userData.avatar,\n                createdAt: new Date(userData.createdAt),\n                lastLogin: new Date(userData.lastLogin)\n            };\n        } catch (error) {\n            console.error(\"Get current user error:\", error);\n            return null;\n        }\n    }\n    async refreshSession() {\n        try {\n            const user = await this.getCurrentUser();\n            return !!user;\n        } catch (error) {\n            console.error(\"Refresh session error:\", error);\n            return false;\n        }\n    }\n    async changePassword(currentPassword, newPassword) {\n        try {\n            if (!this.currentSession) {\n                return {\n                    success: false,\n                    error: \"Not authenticated\"\n                };\n            }\n            // Validate new password\n            if (!this.isValidPassword(newPassword)) {\n                return {\n                    success: false,\n                    error: \"Password must be at least 8 characters long\"\n                };\n            }\n            // Get current user\n            const user = await this.getCurrentUser();\n            if (!user) {\n                return {\n                    success: false,\n                    error: \"User not found\"\n                };\n            }\n            // Verify current password\n            const authResult = await _database__WEBPACK_IMPORTED_MODULE_0__.databaseService.authenticateUser(user.email, currentPassword);\n            if (!authResult) {\n                return {\n                    success: false,\n                    error: \"Current password is incorrect\"\n                };\n            }\n            // Update password (simplified - in real app would update in database)\n            const users = JSON.parse(localStorage.getItem(\"kodekilat-users\") || \"[]\");\n            const userIndex = users.findIndex((u)=>u.id === user.id);\n            if (userIndex >= 0) {\n                const encoder = new TextEncoder();\n                const data = encoder.encode(newPassword + \"kodekilat-salt\");\n                const hashBuffer = await crypto.subtle.digest(\"SHA-256\", data);\n                const hashArray = Array.from(new Uint8Array(hashBuffer));\n                const passwordHash = hashArray.map((b)=>b.toString(16).padStart(2, \"0\")).join(\"\");\n                users[userIndex].passwordHash = passwordHash;\n                localStorage.setItem(\"kodekilat-users\", JSON.stringify(users));\n            }\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"Change password error:\", error);\n            return {\n                success: false,\n                error: \"Failed to change password\"\n            };\n        }\n    }\n    // Validation helpers\n    validateRegistrationData(data) {\n        if (!data.username || data.username.length < 3) {\n            return {\n                isValid: false,\n                error: \"Username must be at least 3 characters long\"\n            };\n        }\n        if (!data.email || !this.isValidEmail(data.email)) {\n            return {\n                isValid: false,\n                error: \"Invalid email format\"\n            };\n        }\n        if (!data.password || !this.isValidPassword(data.password)) {\n            return {\n                isValid: false,\n                error: \"Password must be at least 8 characters long\"\n            };\n        }\n        if (data.password !== data.confirmPassword) {\n            return {\n                isValid: false,\n                error: \"Passwords do not match\"\n            };\n        }\n        return {\n            isValid: true\n        };\n    }\n    isValidEmail(email) {\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        return emailRegex.test(email);\n    }\n    isValidPassword(password) {\n        return password.length >= 8;\n    }\n    // Initialize demo data\n    async initializeDemo() {\n        await _database__WEBPACK_IMPORTED_MODULE_0__.databaseService.initializeDemoData();\n    }\n    // Get session info\n    getCurrentSession() {\n        return this.currentSession;\n    }\n    isSessionValid() {\n        if (!this.currentSession) return false;\n        return new Date(this.currentSession.expiresAt) > new Date();\n    }\n    constructor(){\n        this.currentSession = null;\n    }\n}\nconst authService = AuthService.getInstance();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/database.ts":
/*!**********************************!*\
  !*** ./src/services/database.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   databaseService: () => (/* binding */ databaseService)\n/* harmony export */ });\n// KodeKilat Studio ⚡ - Database Service\n// Client-side database service yang berkomunikasi dengan Electron main process\nclass DatabaseService {\n    static getInstance() {\n        if (!DatabaseService.instance) {\n            DatabaseService.instance = new DatabaseService();\n        }\n        return DatabaseService.instance;\n    }\n    // User Management\n    async createUser(userData) {\n        try {\n            // Hash password\n            const passwordHash = await this.hashPassword(userData.password);\n            const user = {\n                id: this.generateId(),\n                username: userData.username,\n                email: userData.email,\n                passwordHash,\n                avatar: userData.avatar,\n                createdAt: new Date(),\n                lastLogin: new Date()\n            };\n            // Store in localStorage for now (will be replaced with SQLite via IPC)\n            const users = this.getStoredUsers();\n            users.push(user);\n            localStorage.setItem(\"kodekilat-users\", JSON.stringify(users));\n            return {\n                id: user.id,\n                username: user.username,\n                email: user.email,\n                avatar: user.avatar,\n                createdAt: user.createdAt,\n                lastLogin: user.lastLogin\n            };\n        } catch (error) {\n            throw new Error(`Failed to create user: ${error}`);\n        }\n    }\n    async authenticateUser(email, password) {\n        try {\n            const users = this.getStoredUsers();\n            const user = users.find((u)=>u.email === email);\n            if (!user) {\n                return null;\n            }\n            const isValidPassword = await this.verifyPassword(password, user.passwordHash);\n            if (!isValidPassword) {\n                return null;\n            }\n            // Update last login\n            user.lastLogin = new Date();\n            const userIndex = users.findIndex((u)=>u.id === user.id);\n            users[userIndex] = user;\n            localStorage.setItem(\"kodekilat-users\", JSON.stringify(users));\n            return {\n                id: user.id,\n                username: user.username,\n                email: user.email,\n                avatar: user.avatar,\n                createdAt: new Date(user.createdAt),\n                lastLogin: new Date(user.lastLogin)\n            };\n        } catch (error) {\n            throw new Error(`Authentication failed: ${error}`);\n        }\n    }\n    async getUserByEmail(email) {\n        const users = this.getStoredUsers();\n        const user = users.find((u)=>u.email === email);\n        if (!user) return null;\n        return {\n            id: user.id,\n            username: user.username,\n            email: user.email,\n            avatar: user.avatar,\n            createdAt: new Date(user.createdAt),\n            lastLogin: new Date(user.lastLogin)\n        };\n    }\n    // Session Management\n    async createSession(userId) {\n        const session = {\n            id: this.generateId(),\n            userId,\n            token: this.generateToken(),\n            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n            isActive: true\n        };\n        const sessions = this.getStoredSessions();\n        sessions.push(session);\n        localStorage.setItem(\"kodekilat-sessions\", JSON.stringify(sessions));\n        return session;\n    }\n    async getSessionByToken(token) {\n        const sessions = this.getStoredSessions();\n        const session = sessions.find((s)=>s.token === token && s.isActive);\n        if (!session) return null;\n        // Check if session is expired\n        if (new Date(session.expiresAt) < new Date()) {\n            await this.invalidateSession(token);\n            return null;\n        }\n        return {\n            ...session,\n            expiresAt: new Date(session.expiresAt)\n        };\n    }\n    async invalidateSession(token) {\n        const sessions = this.getStoredSessions();\n        const sessionIndex = sessions.findIndex((s)=>s.token === token);\n        if (sessionIndex >= 0) {\n            sessions[sessionIndex].isActive = false;\n            localStorage.setItem(\"kodekilat-sessions\", JSON.stringify(sessions));\n        }\n    }\n    // Workspace Management\n    async createWorkspace(workspaceData) {\n        const workspace = {\n            id: this.generateId(),\n            name: workspaceData.name,\n            path: workspaceData.path,\n            lastOpened: new Date(),\n            isActive: false,\n            settings: workspaceData.settings || {}\n        };\n        const workspaces = this.getStoredWorkspaces();\n        workspaces.push(workspace);\n        localStorage.setItem(\"kodekilat-workspaces\", JSON.stringify(workspaces));\n        return workspace;\n    }\n    async getWorkspaces() {\n        const workspaces = this.getStoredWorkspaces();\n        return workspaces.map((w)=>({\n                ...w,\n                lastOpened: new Date(w.lastOpened)\n            }));\n    }\n    async setActiveWorkspace(workspaceId) {\n        const workspaces = this.getStoredWorkspaces();\n        // Deactivate all workspaces\n        workspaces.forEach((w)=>w.isActive = false);\n        // Activate selected workspace\n        const workspace = workspaces.find((w)=>w.id === workspaceId);\n        if (workspace) {\n            workspace.isActive = true;\n            workspace.lastOpened = new Date();\n        }\n        localStorage.setItem(\"kodekilat-workspaces\", JSON.stringify(workspaces));\n    }\n    // Helper methods\n    getStoredUsers() {\n        const stored = localStorage.getItem(\"kodekilat-users\");\n        return stored ? JSON.parse(stored) : [];\n    }\n    getStoredSessions() {\n        const stored = localStorage.getItem(\"kodekilat-sessions\");\n        return stored ? JSON.parse(stored) : [];\n    }\n    getStoredWorkspaces() {\n        const stored = localStorage.getItem(\"kodekilat-workspaces\");\n        return stored ? JSON.parse(stored) : [];\n    }\n    generateId() {\n        return Date.now().toString(36) + Math.random().toString(36).substr(2);\n    }\n    generateToken() {\n        return Array.from(crypto.getRandomValues(new Uint8Array(32))).map((b)=>b.toString(16).padStart(2, \"0\")).join(\"\");\n    }\n    async hashPassword(password) {\n        // Simple hash for demo - in production use proper bcrypt\n        const encoder = new TextEncoder();\n        const data = encoder.encode(password + \"kodekilat-salt\");\n        const hashBuffer = await crypto.subtle.digest(\"SHA-256\", data);\n        const hashArray = Array.from(new Uint8Array(hashBuffer));\n        return hashArray.map((b)=>b.toString(16).padStart(2, \"0\")).join(\"\");\n    }\n    async verifyPassword(password, hash) {\n        const passwordHash = await this.hashPassword(password);\n        return passwordHash === hash;\n    }\n    // Initialize demo data\n    async initializeDemoData() {\n        const users = this.getStoredUsers();\n        if (users.length === 0) {\n            // Create demo user\n            await this.createUser({\n                username: \"developer\",\n                email: \"<EMAIL>\",\n                password: \"password123\",\n                avatar: \"⚡\"\n            });\n        }\n    }\n}\nconst databaseService = DatabaseService.getInstance();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/database.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/app-store.ts":
/*!*********************************!*\
  !*** ./src/stores/app-store.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppStore: () => (/* binding */ useAppStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(ssr)/../../node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware_immer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zustand/middleware/immer */ \"(ssr)/../../node_modules/zustand/esm/middleware/immer.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/../../node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _shared_config_themes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../shared/config/themes */ \"(ssr)/../../shared/config/themes.ts\");\n/* harmony import */ var _services_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/auth */ \"(ssr)/./src/services/auth.ts\");\n/* harmony import */ var _services_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/database */ \"(ssr)/./src/services/database.ts\");\n\n\n\n\n\n\nconst defaultSettings = {\n    general: {\n        theme: \"cyberpunk\",\n        language: \"en\",\n        autoUpdate: true,\n        telemetry: false\n    },\n    editor: {\n        fontSize: 14,\n        fontFamily: \"JetBrains Mono\",\n        tabSize: 2,\n        insertSpaces: true,\n        wordWrap: false,\n        minimap: true,\n        lineNumbers: true,\n        autoSave: true\n    },\n    terminal: {\n        shell: process.platform === \"win32\" ? \"powershell.exe\" : \"bash\",\n        fontSize: 14,\n        fontFamily: \"JetBrains Mono\",\n        cursorStyle: \"block\"\n    },\n    ai: {\n        defaultProvider: \"huggingface\",\n        defaultModel: \"microsoft/DialoGPT-medium\",\n        autoComplete: true,\n        contextLines: 50\n    },\n    git: {\n        autoFetch: true,\n        confirmSync: true,\n        defaultBranch: \"main\"\n    },\n    shortcuts: {}\n};\nconst useAppStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_4__.subscribeWithSelector)((0,zustand_middleware_immer__WEBPACK_IMPORTED_MODULE_5__.immer)((set, get)=>({\n        // Initial State\n        user: null,\n        isAuthenticated: false,\n        workspace: null,\n        recentWorkspaces: [],\n        activeEditor: null,\n        editors: [],\n        activeTerminal: null,\n        terminals: [],\n        activeAISession: null,\n        aiSessions: [],\n        theme: _shared_config_themes__WEBPACK_IMPORTED_MODULE_0__.cyberpunkTheme,\n        settings: defaultSettings,\n        sidebarVisible: true,\n        terminalVisible: false,\n        aiPanelVisible: false,\n        commandPaletteVisible: false,\n        isLoading: false,\n        isInitialized: false,\n        // Actions\n        initializeApp: async ()=>{\n            set((state)=>{\n                state.isLoading = true;\n            });\n            try {\n                // Initialize demo data\n                await _services_auth__WEBPACK_IMPORTED_MODULE_1__.authService.initializeDemo();\n                // Check authentication status\n                await get().checkAuthStatus();\n                // Load saved settings\n                const savedSettings = localStorage.getItem(\"kodekilat-settings\");\n                if (savedSettings) {\n                    const settings = JSON.parse(savedSettings);\n                    set((state)=>{\n                        state.settings = {\n                            ...defaultSettings,\n                            ...settings\n                        };\n                    });\n                }\n                // Load recent workspaces\n                const workspaces = await _services_database__WEBPACK_IMPORTED_MODULE_2__.databaseService.getWorkspaces();\n                set((state)=>{\n                    state.recentWorkspaces = workspaces.slice(0, 10);\n                    // Set active workspace if any\n                    const activeWorkspace = workspaces.find((w)=>w.isActive);\n                    if (activeWorkspace) {\n                        state.workspace = activeWorkspace;\n                    }\n                });\n                set((state)=>{\n                    state.isInitialized = true;\n                });\n            } catch (error) {\n                console.error(\"Failed to initialize app:\", error);\n            } finally{\n                set((state)=>{\n                    state.isLoading = false;\n                });\n            }\n        },\n        checkAuthStatus: async ()=>{\n            try {\n                const user = await _services_auth__WEBPACK_IMPORTED_MODULE_1__.authService.getCurrentUser();\n                set((state)=>{\n                    state.user = user;\n                    state.isAuthenticated = !!user;\n                });\n            } catch (error) {\n                console.error(\"Failed to check auth status:\", error);\n                set((state)=>{\n                    state.user = null;\n                    state.isAuthenticated = false;\n                });\n            }\n        },\n        setUser: (user)=>{\n            set((state)=>{\n                state.user = user;\n                state.isAuthenticated = !!user;\n            });\n        },\n        login: async (email, password)=>{\n            try {\n                set((state)=>{\n                    state.isLoading = true;\n                });\n                const result = await _services_auth__WEBPACK_IMPORTED_MODULE_1__.authService.login({\n                    email,\n                    password\n                });\n                if (result.success && result.user) {\n                    set((state)=>{\n                        state.user = result.user;\n                        state.isAuthenticated = true;\n                    });\n                }\n                return result;\n            } catch (error) {\n                console.error(\"Login error:\", error);\n                return {\n                    success: false,\n                    error: \"Login failed. Please try again.\"\n                };\n            } finally{\n                set((state)=>{\n                    state.isLoading = false;\n                });\n            }\n        },\n        register: async (data)=>{\n            try {\n                set((state)=>{\n                    state.isLoading = true;\n                });\n                const result = await _services_auth__WEBPACK_IMPORTED_MODULE_1__.authService.register(data);\n                if (result.success && result.user) {\n                    set((state)=>{\n                        state.user = result.user;\n                        state.isAuthenticated = true;\n                    });\n                }\n                return result;\n            } catch (error) {\n                console.error(\"Registration error:\", error);\n                return {\n                    success: false,\n                    error: \"Registration failed. Please try again.\"\n                };\n            } finally{\n                set((state)=>{\n                    state.isLoading = false;\n                });\n            }\n        },\n        logout: async ()=>{\n            try {\n                await _services_auth__WEBPACK_IMPORTED_MODULE_1__.authService.logout();\n                set((state)=>{\n                    state.user = null;\n                    state.isAuthenticated = false;\n                    state.workspace = null;\n                    state.editors = [];\n                    state.activeEditor = null;\n                    state.terminals = [];\n                    state.activeTerminal = null;\n                    state.aiSessions = [];\n                    state.activeAISession = null;\n                });\n            } catch (error) {\n                console.error(\"Logout error:\", error);\n            }\n        },\n        setWorkspace: (workspace)=>{\n            set((state)=>{\n                state.workspace = workspace;\n            });\n            if (workspace) {\n                localStorage.setItem(\"kodekilat-workspace\", JSON.stringify(workspace));\n                get().addRecentWorkspace(workspace);\n            } else {\n                localStorage.removeItem(\"kodekilat-workspace\");\n            }\n        },\n        openWorkspace: async (path)=>{\n            try {\n                // Create or get existing workspace\n                let workspace = get().recentWorkspaces.find((w)=>w.path === path);\n                if (!workspace) {\n                    workspace = await _services_database__WEBPACK_IMPORTED_MODULE_2__.databaseService.createWorkspace({\n                        name: path.split(\"/\").pop() || path.split(\"\\\\\").pop() || \"Untitled\",\n                        path,\n                        settings: {\n                            theme: \"cyberpunk\",\n                            fontSize: 14,\n                            tabSize: 2,\n                            autoSave: true,\n                            wordWrap: false,\n                            minimap: true\n                        }\n                    });\n                }\n                // Set as active workspace\n                await _services_database__WEBPACK_IMPORTED_MODULE_2__.databaseService.setActiveWorkspace(workspace.id);\n                get().setWorkspace(workspace);\n            } catch (error) {\n                console.error(\"Failed to open workspace:\", error);\n            }\n        },\n        closeWorkspace: ()=>{\n            get().setWorkspace(null);\n            set((state)=>{\n                state.editors = [];\n                state.activeEditor = null;\n                state.terminals = [];\n                state.activeTerminal = null;\n            });\n        },\n        addRecentWorkspace: (workspace)=>{\n            set((state)=>{\n                const existing = state.recentWorkspaces.findIndex((w)=>w.path === workspace.path);\n                if (existing >= 0) {\n                    state.recentWorkspaces.splice(existing, 1);\n                }\n                state.recentWorkspaces.unshift(workspace);\n                state.recentWorkspaces = state.recentWorkspaces.slice(0, 10); // Keep only 10 recent\n            });\n            localStorage.setItem(\"kodekilat-recent-workspaces\", JSON.stringify(get().recentWorkspaces));\n        },\n        setActiveEditor: (editor)=>{\n            set((state)=>{\n                state.activeEditor = editor;\n                // Update active state for all editors\n                state.editors.forEach((e)=>{\n                    e.isActive = e.id === editor?.id;\n                });\n            });\n        },\n        addEditor: (editor)=>{\n            set((state)=>{\n                const existing = state.editors.find((e)=>e.filePath === editor.filePath);\n                if (!existing) {\n                    state.editors.push(editor);\n                }\n            });\n            get().setActiveEditor(editor);\n        },\n        removeEditor: (editorId)=>{\n            set((state)=>{\n                const index = state.editors.findIndex((e)=>e.id === editorId);\n                if (index >= 0) {\n                    state.editors.splice(index, 1);\n                    // Set new active editor if the removed one was active\n                    if (state.activeEditor?.id === editorId) {\n                        const newActive = state.editors[Math.max(0, index - 1)] || null;\n                        state.activeEditor = newActive;\n                    }\n                }\n            });\n        },\n        updateEditor: (editorId, updates)=>{\n            set((state)=>{\n                const editor = state.editors.find((e)=>e.id === editorId);\n                if (editor) {\n                    Object.assign(editor, updates);\n                }\n            });\n        },\n        setActiveTerminal: (terminal)=>{\n            set((state)=>{\n                state.activeTerminal = terminal;\n                state.terminals.forEach((t)=>{\n                    t.isActive = t.id === terminal?.id;\n                });\n            });\n        },\n        addTerminal: (terminal)=>{\n            set((state)=>{\n                state.terminals.push(terminal);\n            });\n            get().setActiveTerminal(terminal);\n        },\n        removeTerminal: (terminalId)=>{\n            set((state)=>{\n                const index = state.terminals.findIndex((t)=>t.id === terminalId);\n                if (index >= 0) {\n                    state.terminals.splice(index, 1);\n                    if (state.activeTerminal?.id === terminalId) {\n                        const newActive = state.terminals[Math.max(0, index - 1)] || null;\n                        state.activeTerminal = newActive;\n                    }\n                }\n            });\n        },\n        setActiveAISession: (session)=>{\n            set((state)=>{\n                state.activeAISession = session;\n            });\n        },\n        addAISession: (session)=>{\n            set((state)=>{\n                state.aiSessions.push(session);\n            });\n            get().setActiveAISession(session);\n        },\n        removeAISession: (sessionId)=>{\n            set((state)=>{\n                const index = state.aiSessions.findIndex((s)=>s.id === sessionId);\n                if (index >= 0) {\n                    state.aiSessions.splice(index, 1);\n                    if (state.activeAISession?.id === sessionId) {\n                        const newActive = state.aiSessions[Math.max(0, index - 1)] || null;\n                        state.activeAISession = newActive;\n                    }\n                }\n            });\n        },\n        setTheme: (theme)=>{\n            set((state)=>{\n                state.theme = theme;\n            });\n        },\n        updateSettings: (updates)=>{\n            set((state)=>{\n                state.settings = {\n                    ...state.settings,\n                    ...updates\n                };\n            });\n            localStorage.setItem(\"kodekilat-settings\", JSON.stringify(get().settings));\n        },\n        toggleSidebar: ()=>{\n            set((state)=>{\n                state.sidebarVisible = !state.sidebarVisible;\n            });\n        },\n        toggleTerminal: ()=>{\n            set((state)=>{\n                state.terminalVisible = !state.terminalVisible;\n            });\n        },\n        toggleAIPanel: ()=>{\n            set((state)=>{\n                state.aiPanelVisible = !state.aiPanelVisible;\n            });\n        },\n        toggleCommandPalette: ()=>{\n            set((state)=>{\n                state.commandPaletteVisible = !state.commandPaletteVisible;\n            });\n        },\n        setLoading: (loading)=>{\n            set((state)=>{\n                state.isLoading = loading;\n            });\n        }\n    }))));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/app-store.ts\n");

/***/ }),

/***/ "(ssr)/../../shared/config/themes.ts":
/*!*************************************!*\
  !*** ../../shared/config/themes.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   classicTheme: () => (/* binding */ classicTheme),\n/* harmony export */   cyberpunkTheme: () => (/* binding */ cyberpunkTheme),\n/* harmony export */   defaultTheme: () => (/* binding */ defaultTheme),\n/* harmony export */   matrixTheme: () => (/* binding */ matrixTheme),\n/* harmony export */   themes: () => (/* binding */ themes)\n/* harmony export */ });\n// KodeKilat Studio ⚡ - Cyberpunk Themes Configuration\nconst matrixTheme = {\n    id: \"matrix\",\n    name: \"Matrix Green\",\n    type: \"matrix\",\n    colors: {\n        primary: \"#00ff41\",\n        secondary: \"#008f11\",\n        accent: \"#00ff88\",\n        background: \"#0d1117\",\n        surface: \"#161b22\",\n        text: \"#00ff41\",\n        textSecondary: \"#7d8590\",\n        border: \"#30363d\",\n        success: \"#00ff41\",\n        warning: \"#ffab00\",\n        error: \"#ff4757\",\n        info: \"#00d2ff\"\n    },\n    syntax: {\n        keyword: \"#00ff41\",\n        string: \"#00ff88\",\n        number: \"#00d2ff\",\n        comment: \"#7d8590\",\n        function: \"#ffab00\",\n        variable: \"#ffffff\",\n        type: \"#ff6b9d\"\n    }\n};\nconst cyberpunkTheme = {\n    id: \"cyberpunk\",\n    name: \"Cyberpunk Neon\",\n    type: \"cyberpunk\",\n    colors: {\n        primary: \"#ff0080\",\n        secondary: \"#8000ff\",\n        accent: \"#00ffff\",\n        background: \"#0a0a0a\",\n        surface: \"#1a1a2e\",\n        text: \"#ffffff\",\n        textSecondary: \"#a0a0a0\",\n        border: \"#16213e\",\n        success: \"#00ff80\",\n        warning: \"#ffaa00\",\n        error: \"#ff0040\",\n        info: \"#0080ff\"\n    },\n    syntax: {\n        keyword: \"#ff0080\",\n        string: \"#00ffff\",\n        number: \"#ff8000\",\n        comment: \"#666666\",\n        function: \"#8000ff\",\n        variable: \"#ffffff\",\n        type: \"#00ff80\"\n    }\n};\nconst classicTheme = {\n    id: \"classic\",\n    name: \"Classic Dark\",\n    type: \"classic\",\n    colors: {\n        primary: \"#007acc\",\n        secondary: \"#005a9e\",\n        accent: \"#0099ff\",\n        background: \"#1e1e1e\",\n        surface: \"#252526\",\n        text: \"#cccccc\",\n        textSecondary: \"#969696\",\n        border: \"#3e3e42\",\n        success: \"#4caf50\",\n        warning: \"#ff9800\",\n        error: \"#f44336\",\n        info: \"#2196f3\"\n    },\n    syntax: {\n        keyword: \"#569cd6\",\n        string: \"#ce9178\",\n        number: \"#b5cea8\",\n        comment: \"#6a9955\",\n        function: \"#dcdcaa\",\n        variable: \"#9cdcfe\",\n        type: \"#4ec9b0\"\n    }\n};\nconst themes = [\n    matrixTheme,\n    cyberpunkTheme,\n    classicTheme\n];\nconst defaultTheme = cyberpunkTheme;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../shared/config/themes.ts\n");

/***/ }),

/***/ "(ssr)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dc2b7b6ac5e2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGtvZGVraWxhdC9zdHVkaW8vLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzP2ExZDUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkYzJiN2I2YWM1ZTJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\kodekilat-newsss\apps\studio\src\app\layout.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/immer","vendor-chunks/zustand","vendor-chunks/use-sync-external-store"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2F..%2F..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CUsers%5CDownloads%5Ckodekilat-newsss%5Capps%5Cstudio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5Ckodekilat-newsss%5Capps%5Cstudio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();