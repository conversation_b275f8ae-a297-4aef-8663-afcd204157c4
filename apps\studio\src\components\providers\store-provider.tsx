'use client';

import { createContext, useContext, useEffect } from 'react';
import { useAppStore } from '../../stores/app-store';

interface StoreContextType {
  // Add any store-specific context if needed
}

const StoreContext = createContext<StoreContextType | undefined>(undefined);

export function StoreProvider({ children }: { children: React.ReactNode }) {
  const initializeApp = useAppStore(state => state.initializeApp);

  useEffect(() => {
    // Initialize app on mount
    initializeApp();
  }, [initializeApp]);

  return (
    <StoreContext.Provider value={{}}>
      {children}
    </StoreContext.Provider>
  );
}

export function useStore() {
  const context = useContext(StoreContext);
  if (context === undefined) {
    throw new Error('useStore must be used within a StoreProvider');
  }
  return context;
}
