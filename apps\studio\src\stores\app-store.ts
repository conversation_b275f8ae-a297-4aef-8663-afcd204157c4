import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { subscribeWithSelector } from 'zustand/middleware';
import type {
  User,
  Workspace,
  EditorTab,
  Terminal,
  AISession,
  Theme,
  AppSettings
} from '../../../../shared/types';
import { cyberpunkTheme } from '../../../../shared/config/themes';
import { authService } from '@/services/auth';
import { databaseService } from '@/services/database';

interface AppState {
  // User & Auth
  user: User | null;
  isAuthenticated: boolean;
  
  // Workspace
  workspace: Workspace | null;
  recentWorkspaces: Workspace[];
  
  // Editor
  activeEditor: EditorTab | null;
  editors: EditorTab[];
  
  // Terminal
  activeTerminal: Terminal | null;
  terminals: Terminal[];
  
  // AI
  activeAISession: AISession | null;
  aiSessions: AISession[];
  
  // UI State
  theme: Theme;
  settings: AppSettings;
  sidebarVisible: boolean;
  terminalVisible: boolean;
  aiPanelVisible: boolean;
  commandPaletteVisible: boolean;
  
  // Loading states
  isLoading: boolean;
  isInitialized: boolean;
}

interface AppActions {
  // Initialization
  initializeApp: () => Promise<void>;
  
  // User & Auth
  setUser: (user: User | null) => void;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (data: { username: string; email: string; password: string; confirmPassword: string; avatar?: string }) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  checkAuthStatus: () => Promise<void>;
  
  // Workspace
  setWorkspace: (workspace: Workspace | null) => void;
  openWorkspace: (path: string) => Promise<void>;
  closeWorkspace: () => void;
  addRecentWorkspace: (workspace: Workspace) => void;
  
  // Editor
  setActiveEditor: (editor: EditorTab | null) => void;
  addEditor: (editor: EditorTab) => void;
  removeEditor: (editorId: string) => void;
  updateEditor: (editorId: string, updates: Partial<EditorTab>) => void;
  
  // Terminal
  setActiveTerminal: (terminal: Terminal | null) => void;
  addTerminal: (terminal: Terminal) => void;
  removeTerminal: (terminalId: string) => void;
  
  // AI
  setActiveAISession: (session: AISession | null) => void;
  addAISession: (session: AISession) => void;
  removeAISession: (sessionId: string) => void;
  
  // UI Actions
  setTheme: (theme: Theme) => void;
  updateSettings: (updates: Partial<AppSettings>) => void;
  toggleSidebar: () => void;
  toggleTerminal: () => void;
  toggleAIPanel: () => void;
  toggleCommandPalette: () => void;
  
  // Loading
  setLoading: (loading: boolean) => void;
}

const defaultSettings: AppSettings = {
  general: {
    theme: 'cyberpunk',
    language: 'en',
    autoUpdate: true,
    telemetry: false
  },
  editor: {
    fontSize: 14,
    fontFamily: 'JetBrains Mono',
    tabSize: 2,
    insertSpaces: true,
    wordWrap: false,
    minimap: true,
    lineNumbers: true,
    autoSave: true
  },
  terminal: {
    shell: process.platform === 'win32' ? 'powershell.exe' : 'bash',
    fontSize: 14,
    fontFamily: 'JetBrains Mono',
    cursorStyle: 'block'
  },
  ai: {
    defaultProvider: 'huggingface',
    defaultModel: 'microsoft/DialoGPT-medium',
    autoComplete: true,
    contextLines: 50
  },
  git: {
    autoFetch: true,
    confirmSync: true,
    defaultBranch: 'main'
  },
  shortcuts: {}
};

export const useAppStore = create<AppState & AppActions>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // Initial State
      user: null,
      isAuthenticated: false,
      workspace: null,
      recentWorkspaces: [],
      activeEditor: null,
      editors: [],
      activeTerminal: null,
      terminals: [],
      activeAISession: null,
      aiSessions: [],
      theme: cyberpunkTheme,
      settings: defaultSettings,
      sidebarVisible: true,
      terminalVisible: false,
      aiPanelVisible: false,
      commandPaletteVisible: false,
      isLoading: false,
      isInitialized: false,

      // Actions
      initializeApp: async () => {
        set((state) => {
          state.isLoading = true;
        });

        try {
          // Initialize demo data
          await authService.initializeDemo();

          // Check authentication status
          await get().checkAuthStatus();

          // Load saved settings
          const savedSettings = localStorage.getItem('kodekilat-settings');
          if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            set((state) => {
              state.settings = { ...defaultSettings, ...settings };
            });
          }

          // Load recent workspaces
          const workspaces = await databaseService.getWorkspaces();
          set((state) => {
            state.recentWorkspaces = workspaces.slice(0, 10);
            // Set active workspace if any
            const activeWorkspace = workspaces.find(w => w.isActive);
            if (activeWorkspace) {
              state.workspace = activeWorkspace;
            }
          });

          set((state) => {
            state.isInitialized = true;
          });
        } catch (error) {
          console.error('Failed to initialize app:', error);
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },

      checkAuthStatus: async () => {
        try {
          const user = await authService.getCurrentUser();
          set((state) => {
            state.user = user;
            state.isAuthenticated = !!user;
          });
        } catch (error) {
          console.error('Failed to check auth status:', error);
          set((state) => {
            state.user = null;
            state.isAuthenticated = false;
          });
        }
      },

      setUser: (user) => {
        set((state) => {
          state.user = user;
          state.isAuthenticated = !!user;
        });
      },

      login: async (email, password) => {
        try {
          set((state) => {
            state.isLoading = true;
          });

          const result = await authService.login({ email, password });

          if (result.success && result.user) {
            set((state) => {
              state.user = result.user!;
              state.isAuthenticated = true;
            });
          }

          return result;
        } catch (error) {
          console.error('Login error:', error);
          return { success: false, error: 'Login failed. Please try again.' };
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },

      register: async (data) => {
        try {
          set((state) => {
            state.isLoading = true;
          });

          const result = await authService.register(data);

          if (result.success && result.user) {
            set((state) => {
              state.user = result.user!;
              state.isAuthenticated = true;
            });
          }

          return result;
        } catch (error) {
          console.error('Registration error:', error);
          return { success: false, error: 'Registration failed. Please try again.' };
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },

      logout: async () => {
        try {
          await authService.logout();
          set((state) => {
            state.user = null;
            state.isAuthenticated = false;
            state.workspace = null;
            state.editors = [];
            state.activeEditor = null;
            state.terminals = [];
            state.activeTerminal = null;
            state.aiSessions = [];
            state.activeAISession = null;
          });
        } catch (error) {
          console.error('Logout error:', error);
        }
      },

      setWorkspace: (workspace) => {
        set((state) => {
          state.workspace = workspace;
        });
        
        if (workspace) {
          localStorage.setItem('kodekilat-workspace', JSON.stringify(workspace));
          get().addRecentWorkspace(workspace);
        } else {
          localStorage.removeItem('kodekilat-workspace');
        }
      },

      openWorkspace: async (path) => {
        try {
          // Create or get existing workspace
          let workspace = get().recentWorkspaces.find(w => w.path === path);

          if (!workspace) {
            workspace = await databaseService.createWorkspace({
              name: path.split('/').pop() || path.split('\\').pop() || 'Untitled',
              path,
              settings: {
                theme: 'cyberpunk',
                fontSize: 14,
                tabSize: 2,
                autoSave: true,
                wordWrap: false,
                minimap: true
              }
            });
          }

          // Set as active workspace
          await databaseService.setActiveWorkspace(workspace.id);
          get().setWorkspace(workspace);
        } catch (error) {
          console.error('Failed to open workspace:', error);
        }
      },

      closeWorkspace: () => {
        get().setWorkspace(null);
        set((state) => {
          state.editors = [];
          state.activeEditor = null;
          state.terminals = [];
          state.activeTerminal = null;
        });
      },

      addRecentWorkspace: (workspace) => {
        set((state) => {
          const existing = state.recentWorkspaces.findIndex(w => w.path === workspace.path);
          if (existing >= 0) {
            state.recentWorkspaces.splice(existing, 1);
          }
          state.recentWorkspaces.unshift(workspace);
          state.recentWorkspaces = state.recentWorkspaces.slice(0, 10); // Keep only 10 recent
        });
        
        localStorage.setItem('kodekilat-recent-workspaces', JSON.stringify(get().recentWorkspaces));
      },

      setActiveEditor: (editor) => {
        set((state) => {
          state.activeEditor = editor;
          // Update active state for all editors
          state.editors.forEach(e => {
            e.isActive = e.id === editor?.id;
          });
        });
      },

      addEditor: (editor) => {
        set((state) => {
          const existing = state.editors.find(e => e.filePath === editor.filePath);
          if (!existing) {
            state.editors.push(editor);
          }
        });
        get().setActiveEditor(editor);
      },

      removeEditor: (editorId) => {
        set((state) => {
          const index = state.editors.findIndex(e => e.id === editorId);
          if (index >= 0) {
            state.editors.splice(index, 1);
            
            // Set new active editor if the removed one was active
            if (state.activeEditor?.id === editorId) {
              const newActive = state.editors[Math.max(0, index - 1)] || null;
              state.activeEditor = newActive;
            }
          }
        });
      },

      updateEditor: (editorId, updates) => {
        set((state) => {
          const editor = state.editors.find(e => e.id === editorId);
          if (editor) {
            Object.assign(editor, updates);
          }
        });
      },

      setActiveTerminal: (terminal) => {
        set((state) => {
          state.activeTerminal = terminal;
          state.terminals.forEach(t => {
            t.isActive = t.id === terminal?.id;
          });
        });
      },

      addTerminal: (terminal) => {
        set((state) => {
          state.terminals.push(terminal);
        });
        get().setActiveTerminal(terminal);
      },

      removeTerminal: (terminalId) => {
        set((state) => {
          const index = state.terminals.findIndex(t => t.id === terminalId);
          if (index >= 0) {
            state.terminals.splice(index, 1);
            
            if (state.activeTerminal?.id === terminalId) {
              const newActive = state.terminals[Math.max(0, index - 1)] || null;
              state.activeTerminal = newActive;
            }
          }
        });
      },

      setActiveAISession: (session) => {
        set((state) => {
          state.activeAISession = session;
        });
      },

      addAISession: (session) => {
        set((state) => {
          state.aiSessions.push(session);
        });
        get().setActiveAISession(session);
      },

      removeAISession: (sessionId) => {
        set((state) => {
          const index = state.aiSessions.findIndex(s => s.id === sessionId);
          if (index >= 0) {
            state.aiSessions.splice(index, 1);
            
            if (state.activeAISession?.id === sessionId) {
              const newActive = state.aiSessions[Math.max(0, index - 1)] || null;
              state.activeAISession = newActive;
            }
          }
        });
      },

      setTheme: (theme) => {
        set((state) => {
          state.theme = theme;
        });
      },

      updateSettings: (updates) => {
        set((state) => {
          state.settings = { ...state.settings, ...updates };
        });
        localStorage.setItem('kodekilat-settings', JSON.stringify(get().settings));
      },

      toggleSidebar: () => {
        set((state) => {
          state.sidebarVisible = !state.sidebarVisible;
        });
      },

      toggleTerminal: () => {
        set((state) => {
          state.terminalVisible = !state.terminalVisible;
        });
      },

      toggleAIPanel: () => {
        set((state) => {
          state.aiPanelVisible = !state.aiPanelVisible;
        });
      },

      toggleCommandPalette: () => {
        set((state) => {
          state.commandPaletteVisible = !state.commandPaletteVisible;
        });
      },

      setLoading: (loading) => {
        set((state) => {
          state.isLoading = loading;
        });
      }
    }))
  )
);
