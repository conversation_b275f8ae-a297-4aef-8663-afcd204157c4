'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Icon } from '@/components/ui/icon';
import { useAppStore } from '@/stores/app-store';
import {
  Mail,
  Lock,
  Eye,
  EyeOff,
  Loader2,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

interface LoginFormProps {
  onSwitchToRegister: () => void;
  onClose?: () => void;
}

export function LoginForm({ onSwitchToRegister, onClose }: LoginFormProps) {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const { login } = useAppStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      const result = await login(email, password);
      
      if (result.success) {
        onClose?.();
      } else {
        setError(result.error || 'Login failed');
      }
    } catch (error) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoLogin = async () => {
    setEmail('<EMAIL>');
    setPassword('password123');
    setError('');
    setIsLoading(true);

    try {
      const result = await login('<EMAIL>', 'password123');
      
      if (result.success) {
        onClose?.();
      } else {
        setError(result.error || 'Demo login failed');
      }
    } catch (error) {
      setError('Demo login failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="w-full max-w-md p-8 bg-cyber-surface border border-cyber-border rounded-lg glass"
    >
      {/* Header */}
      <div className="text-center mb-8">
        <motion.div
          className="text-4xl mb-4"
          animate={{ rotate: [0, 360] }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        >
          ⚡
        </motion.div>
        <h1 className="text-2xl font-cyber font-bold text-neon-cyan">Welcome Back</h1>
        <p className="text-cyber-muted mt-2">Sign in to KodeKilat Studio</p>
      </div>

      {/* Demo Login Notice */}
      <div className="mb-6 p-3 bg-neon-cyan/10 border border-neon-cyan/30 rounded-lg">
        <div className="flex items-center space-x-2 text-neon-cyan text-sm">
          <Icon icon={CheckCircle} size={16} />
          <span>Demo credentials are pre-filled</span>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6 p-3 bg-red-500/10 border border-red-500/30 rounded-lg"
        >
          <div className="flex items-center space-x-2 text-red-400 text-sm">
            <Icon icon={AlertCircle} size={16} />
            <span>{error}</span>
          </div>
        </motion.div>
      )}

      {/* Login Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-cyber-text mb-2">
            Email
          </label>
          <div className="relative">
            <Icon 
              icon={Mail} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-cyber-muted" 
              size={16} 
            />
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="pl-10"
              placeholder="Enter your email"
              variant="cyber"
              required
              disabled={isLoading}
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-cyber-text mb-2">
            Password
          </label>
          <div className="relative">
            <Icon 
              icon={Lock} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-cyber-muted" 
              size={16} 
            />
            <Input
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="pl-10 pr-10"
              placeholder="Enter your password"
              variant="cyber"
              required
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-cyber-muted hover:text-neon-cyan transition-colors"
              disabled={isLoading}
            >
              <Icon icon={showPassword ? EyeOff : Eye} size={16} />
            </button>
          </div>
        </div>

        <div className="space-y-3">
          <Button 
            type="submit" 
            variant="cyber" 
            className="w-full" 
            glow 
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Icon icon={Loader2} size={16} className="mr-2 animate-spin" />
                Signing In...
              </>
            ) : (
              'Sign In'
            )}
          </Button>

          <Button 
            type="button"
            variant="outline" 
            className="w-full" 
            onClick={handleDemoLogin}
            disabled={isLoading}
          >
            <Icon icon={CheckCircle} size={16} className="mr-2" />
            Demo Login
          </Button>
        </div>
      </form>

      {/* Footer */}
      <div className="mt-8 text-center space-y-4">
        <div className="text-sm text-cyber-muted">
          Don't have an account?{' '}
          <button
            onClick={onSwitchToRegister}
            className="text-neon-cyan hover:text-neon-cyan/80 transition-colors font-medium"
            disabled={isLoading}
          >
            Sign up
          </button>
        </div>

        <div className="text-xs text-cyber-muted">
          <p>Demo Account:</p>
          <p>Email: <EMAIL></p>
          <p>Password: password123</p>
        </div>

        {onClose && (
          <button
            onClick={onClose}
            className="text-sm text-cyber-muted hover:text-cyber-text transition-colors"
            disabled={isLoading}
          >
            Continue without account
          </button>
        )}
      </div>
    </motion.div>
  );
}
