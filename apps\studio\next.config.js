/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    unoptimized: true
  },
  webpack: (config, { isServer }) => {
    // Handle node modules that need to be external
    config.externals = config.externals || [];
    config.externals.push({
      'electron': 'commonjs electron'
    });

    return config;
  },
  experimental: {
    esmExternals: false
  }
};

module.exports = nextConfig;
